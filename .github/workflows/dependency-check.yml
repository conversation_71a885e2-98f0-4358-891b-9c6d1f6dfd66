name: OWASP report

on:
  workflow_dispatch:
  schedule:
    - cron: '0 6 * * 1-5'
  pull_request:
    branches:
      - main
      - 'release-**'
    paths:
      - 'dependencies.gradle'
      - '.github/workflows/dependency-check.yml'

permissions:
  contents: read
  checks: write
  id-token: write
jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21.0.7
          cache: 'gradle'
      - name: Cache Dependency Check data
        uses: actions/cache@v4
        with:
            path: ~/.gradle/caches
            key: ${{ runner.os }}-dependency-check-${{ hashFiles('**/*.gradle') }}
            restore-keys: ${{ runner.os }}-dependency-check-
      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v3
      - name: OWASP Dependency-Check
        run: ./gradlew dependencyCheckAnalyze -Dorg.gradle.parallel=false
        env:
          NIST_NVD_API_KEY: ${{ secrets.NIST_NVD_API_KEY }}
      - name: API OWASP Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: solum-xplain-api-dependency-check-report.html
          path: ./xplain-api/app/build/reports/dependency-check-report.html
          if-no-files-found: error
          retention-days: 10
      - name: Valuations OWASP Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: solum-xplain-valuations-dependency-check-report.html
          path: ./xplain-valuation/app/build/reports/dependency-check-report.html
          if-no-files-found: error
          retention-days: 10
      - name: XVA OWASP Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: solum-xplain-xva-dependency-check-report.html
          path: ./xplain-xva/app/build/reports/dependency-check-report.html
          if-no-files-found: error
          retention-days: 10
