name: <PERSON>pen<PERSON>bot auto-approve
on: pull_request_target

permissions:
  contents: read
  checks: write
  id-token: write
jobs:
  approve-dependabot:
    runs-on: ubuntu-latest
    if: ${{ github.actor == 'dependabot[bot]' }}
    steps:
      - name: Checkout platform repository
        uses: actions/checkout@v4
        with:
          path: "platform"
          ref: 'main'
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./platform/.github/actions/common/dependabot-merge
        with:
          github-token: '${{ secrets.PAT_GITHUB_TOKEN }}'
