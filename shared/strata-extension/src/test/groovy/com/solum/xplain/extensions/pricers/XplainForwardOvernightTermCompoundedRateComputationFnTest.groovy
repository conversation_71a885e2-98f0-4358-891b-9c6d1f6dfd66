package com.solum.xplain.extensions.pricers

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.market.explain.ExplainMap
import com.opengamma.strata.market.explain.ExplainKey
import com.opengamma.strata.pricer.rate.RateComputationFn
import com.solum.xplain.extensions.index.ExtendedOvernightIndices
import com.solum.xplain.extensions.rate.OvernightTermCompoundedRateComputation
import spock.lang.Specification

import java.time.LocalDate

import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

class XplainForwardOvernightTermCompoundedRateComputationFnTest extends Specification {
  static START_DATE = LocalDate.parse("2021-02-08")
  static END_DATE = LocalDate.parse("2022-02-08")
  static VALUATION_DATE = LocalDate.parse("2021-02-10")
  static TERM_FIRST_FIXING_DATE = LocalDate.parse("2021-02-08")

  RateComputationFn<OvernightTermCompoundedRateComputation> FUNCTION = 
      new XplainForwardOvernightTermCompoundedRateComputationFn(ReferenceData.standard())

  def "should calculate rate for overnight term compounded computation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def computation = OvernightTermCompoundedRateComputation.of(
      ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      0,
      TERM_FIRST_FIXING_DATE,
      ReferenceData.standard()
      )

    when:
    def result = FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates)

    then:
    result > 0
    that result, closeTo(0.0004810003d, 0.001d)
  }

  def "should calculate rate sensitivity for overnight term compounded computation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def computation = OvernightTermCompoundedRateComputation.of(
      ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      0,
      TERM_FIRST_FIXING_DATE,
      ReferenceData.standard()
      )

    when:
    def result = FUNCTION.rateSensitivity(computation, START_DATE, VALUATION_DATE, rates)

    then:
    result != null
  }

  def "should calculate rate with explain for overnight term compounded computation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def explain = ExplainMap.builder()
    def computation = OvernightTermCompoundedRateComputation.of(
      ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      0,
      TERM_FIRST_FIXING_DATE,
      ReferenceData.standard()
      )

    when:
    def result = FUNCTION.explainRate(computation, START_DATE, VALUATION_DATE, rates, explain)

    then:
    result > 0
    that result, closeTo(0.0004810003d, 0.001d)
    that explain.build().get(ExplainKey.COMBINED_RATE).get(), closeTo(0.0004810003d, 0.001d)
  }
}
