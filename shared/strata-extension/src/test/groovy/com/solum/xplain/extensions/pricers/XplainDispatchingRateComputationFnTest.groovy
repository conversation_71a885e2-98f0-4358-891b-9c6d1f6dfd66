package com.solum.xplain.extensions.pricers

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.pricer.impl.rate.DispatchingRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardIborAveragedRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardIborInterpolatedRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardIborRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardInflationEndInterpolatedRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardInflationEndMonthRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardInflationInterpolatedRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardInflationMonthlyRateComputationFn
import com.opengamma.strata.pricer.impl.rate.ForwardOvernightAveragedDailyRateComputationFn
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation
import com.solum.xplain.extensions.index.ExtendedOvernightIndices
import com.solum.xplain.extensions.index.OvernightTermRateComputation
import com.solum.xplain.extensions.rate.OvernightTermCompoundedRateComputation
import spock.lang.Specification

import java.time.LocalDate

import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

class XplainDispatchingRateComputationFnTest extends Specification {
  static START_DATE = LocalDate.parse("2021-02-08")
  static END_DATE = LocalDate.parse("2022-02-08")
  static VALUATION_DATE = LocalDate.parse("2021-02-10")
  static TERM_FIRST_FIXING_DATE = LocalDate.parse("2021-02-08")

  def referenceData = ReferenceData.standard()
  
  def standardDispatcher = new DispatchingRateComputationFn(
      ForwardIborRateComputationFn.DEFAULT,
      ForwardIborInterpolatedRateComputationFn.DEFAULT,
      ForwardIborAveragedRateComputationFn.DEFAULT,
      new XplainForwardOvernightCompoundedRateComputation(referenceData),
      new XplainForwardOvernightCompoundedAnnualRateComputationFn(referenceData),
      new XplainApproxForwardOvernightAveragedRateComputationFn(referenceData),
      ForwardOvernightAveragedDailyRateComputationFn.DEFAULT,
      ForwardInflationMonthlyRateComputationFn.DEFAULT,
      ForwardInflationInterpolatedRateComputationFn.DEFAULT,
      ForwardInflationEndMonthRateComputationFn.DEFAULT,
      ForwardInflationEndInterpolatedRateComputationFn.DEFAULT)

  def FUNCTION = new XplainDispatchingRateComputationFn(standardDispatcher, referenceData)

  def "should handle standard OvernightCompoundedRateComputation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.USD_SOFR,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    when:
    def result = FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates)

    then:
    result > 0
    that result, closeTo(0.0004810003d, 0.001d)
  }

  def "should handle custom OvernightTermCompoundedRateComputation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def computation = OvernightTermCompoundedRateComputation.of(
            ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      0,
      TERM_FIRST_FIXING_DATE,
      ReferenceData.standard()
      )

    when:
    def result = FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates)

    then:
    result > 0
    that result, closeTo(0.0004810003d, 0.001d)
  }

  def "should handle OvernightTermRateComputation"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def delegate = OvernightCompoundedRateComputation.of(
      OvernightIndices.USD_SOFR,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )
    def computation = OvernightTermRateComputation.of(delegate, TERM_FIRST_FIXING_DATE)

    when:
    def result = FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates)

    then:
    result > 0
    that result, closeTo(0.0004810003d, 0.001d)
  }

  def "should handle rate sensitivity for all computation types"() {
    setup:
    def rates = CalibrationRatesSample.jointRatesProvider(VALUATION_DATE)
    def standardComputation = OvernightCompoundedRateComputation.of(
      OvernightIndices.USD_SOFR,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )
    def termCompoundedComputation = OvernightTermCompoundedRateComputation.of(
            ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      0,
      TERM_FIRST_FIXING_DATE,
      ReferenceData.standard()
      )
    def termComputation = OvernightTermRateComputation.of(standardComputation, TERM_FIRST_FIXING_DATE)

    when:
    def standardResult = FUNCTION.rateSensitivity(standardComputation, START_DATE, VALUATION_DATE, rates)
    def termCompoundedResult = FUNCTION.rateSensitivity(termCompoundedComputation, START_DATE, VALUATION_DATE, rates)
    def termResult = FUNCTION.rateSensitivity(termComputation, START_DATE, VALUATION_DATE, rates)

    then:
    standardResult != null
    termCompoundedResult != null
    termResult != null
  }
}
