package com.solum.xplain.extensions.index;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.schedule.Schedule;
import com.opengamma.strata.basics.schedule.SchedulePeriod;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.product.rate.RateComputation;
import com.opengamma.strata.product.swap.NegativeRateMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.RateAccrualPeriod;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.SwapLegType;
import com.solum.xplain.extensions.rate.OvernightTermRateComputation;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public final class OvernightTermRateCalculation implements RateCalculation {

  private final OvernightTermIndex index;
  private final DayCount dayCount;
  private final OvernightAccrualMethod accrualMethod;
  private final NegativeRateMethod negativeRateMethod;
  private final int rateCutOffDays;
  private final ValueSchedule gearing;
  private final ValueSchedule spread;

  private static final ValueSchedule DEFAULT_GEARING = ValueSchedule.of(1d);
  private static final ValueSchedule DEFAULT_SPREAD = ValueSchedule.of(0d);

  public OvernightTermRateCalculation(
      OvernightTermIndex index,
      DayCount dayCount,
      OvernightAccrualMethod accrualMethod,
      NegativeRateMethod negativeRateMethod,
      int rateCutOffDays,
      ValueSchedule gearing,
      ValueSchedule spread) {

    this.index = ArgChecker.notNull(index, "index");
    this.dayCount = dayCount != null ? dayCount : index.getDayCount();
    this.accrualMethod = accrualMethod != null ? accrualMethod : OvernightAccrualMethod.COMPOUNDED;
    this.negativeRateMethod =
        negativeRateMethod != null ? negativeRateMethod : NegativeRateMethod.ALLOW_NEGATIVE;
    this.rateCutOffDays = ArgChecker.notNegative(rateCutOffDays, "rateCutOffDays");
    this.gearing = gearing != null ? gearing : DEFAULT_GEARING;
    this.spread = spread != null ? spread : DEFAULT_SPREAD;
  }

  public static OvernightTermRateCalculation of(OvernightTermIndex index) {
    return new OvernightTermRateCalculation(index, null, null, null, 0, null, null);
  }

  @Override
  public SwapLegType getType() {
    return SwapLegType.OTHER; // needs to be OVERNIGHT TERM
  }

  @Override
  public void collectCurrencies(ImmutableSet.Builder<Currency> builder) {
    builder.add(index.getCurrency());
  }

  @Override
  public void collectIndices(ImmutableSet.Builder<Index> builder) {
    builder.add(index);
  }

  @Override
  public ImmutableList<RateAccrualPeriod> createAccrualPeriods(
      Schedule accrualSchedule, Schedule paymentSchedule, ReferenceData refData) {
    DoubleArray resolvedGearings =
        MoreObjects.firstNonNull(this.gearing, DEFAULT_GEARING).resolveValues(accrualSchedule);
    DoubleArray resolvedSpreads =
        MoreObjects.firstNonNull(this.spread, DEFAULT_SPREAD).resolveValues(accrualSchedule);
    ImmutableList.Builder<RateAccrualPeriod> accrualPeriods = ImmutableList.builder();

    for (int i = 0; i < accrualSchedule.size(); ++i) {
      SchedulePeriod period = accrualSchedule.getPeriod(i);
      double yearFraction = period.yearFraction(this.dayCount, accrualSchedule);
      RateComputation rateComputation =
          this.createRateComputation(period, paymentSchedule, refData);
      RateAccrualPeriod accrualPeriod =
          RateAccrualPeriod.builder()
              .startDate(period.getStartDate())
              .endDate(period.getEndDate())
              .yearFraction(yearFraction)
              .rateComputation(rateComputation)
              .gearing(resolvedGearings.get(i))
              .spread(resolvedSpreads.get(i))
              .negativeRateMethod(this.negativeRateMethod)
              .build();
      accrualPeriods.add(accrualPeriod);
    }

    return accrualPeriods.build();
  }

  private RateComputation createRateComputation(
      SchedulePeriod period, Schedule paymentSchedule, ReferenceData refData) {
    int effectiveRateCutOffDaysOffset =
        this.isLastAccrualInPaymentPeriod(period, paymentSchedule) ? this.rateCutOffDays : 0;
    return OvernightTermRateComputation.of(
        this.index,
        period.getStartDate(),
        period.getEndDate(),
        effectiveRateCutOffDaysOffset,
        this.accrualMethod,
        refData);
  }

  private boolean isLastAccrualInPaymentPeriod(SchedulePeriod period, Schedule paymentSchedule) {
    return rateCutOffDays == 0
        || paymentSchedule.getPeriods().stream()
            .anyMatch(pp -> pp.getUnadjustedEndDate().equals(period.getUnadjustedEndDate()));
  }
}
