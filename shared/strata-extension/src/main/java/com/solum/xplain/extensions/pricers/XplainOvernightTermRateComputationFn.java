package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.solum.xplain.extensions.index.OvernightTermRateComputation;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;

/**
 * Rate computation function for OvernightTermRateComputation.
 *
 * <p>This function delegates to XplainForwardOvernightCompoundedRateComputation but uses a custom
 * termFirstFixingDate from the OvernightTermRateComputation.
 */
@RequiredArgsConstructor
public class XplainOvernightTermRateComputationFn
    implements RateComputationFn<OvernightTermRateComputation> {

  private final ReferenceData referenceData;

  @Override
  public double rate(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    XplainForwardOvernightCompoundedRateComputation delegate =
        new XplainForwardOvernightCompoundedRateComputation(referenceData);
    return delegate.rate(
        computation.getDelegate(),
        startDate,
        endDate,
        provider,
        computation.getTermFirstFixingDate());
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    XplainForwardOvernightCompoundedRateComputation delegate =
        new XplainForwardOvernightCompoundedRateComputation(referenceData);

    var rates = provider.overnightIndexRates(computation.getDelegate().getIndex());
    var isOvernightTerm =
        computation.getDelegate().getIndex()
            instanceof com.solum.xplain.extensions.index.OvernightTermIndex;

    return delegate.rateSensitivity(
        computation.getDelegate(),
        startDate,
        endDate,
        rates,
        referenceData,
        isOvernightTerm,
        computation.getTermFirstFixingDate());
  }

  @Override
  public double explainRate(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }
}
