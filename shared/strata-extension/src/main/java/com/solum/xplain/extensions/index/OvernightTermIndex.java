package com.solum.xplain.extensions.index;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.FloatingRateName;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jspecify.annotations.NullMarked;

@NullMarked
@Data
@EqualsAndHashCode
public class OvernightTermIndex implements RateIndex, Named, Serializable {

  @Serial private static final long serialVersionUID = 1L;
  private final String name;
  private final Currency currency;
  private final boolean active;
  private final HolidayCalendarId fixingCalendar;
  private final DaysAdjustment fixingDateOffset;
  private final DayCount dayCount;
  private final Tenor tenor;
  private final String floatingRateName;

  public OvernightTermIndex(
      String name,
      Currency currency,
      boolean active,
      HolidayCalendarId fixingCalendar,
      DaysAdjustment fixingDateOffset,
      DayCount dayCount,
      Tenor tenor) {
    this.name = name;
    this.currency = currency;
    this.active = active;
    this.fixingCalendar = fixingCalendar;
    this.fixingDateOffset = fixingDateOffset;
    this.dayCount = dayCount;
    this.tenor = tenor;
    String suffix = "-" + tenor;
    if (!name.endsWith(suffix)) {
      throw new IllegalArgumentException(
          Messages.format(
              "OvernightTermIndex name '{}' must end with tenor '{}'", name, tenor.toString()));
    } else {
      this.floatingRateName = name.substring(0, name.length() - suffix.length());
    }
  }

  public static OvernightTermIndex of(String name) {
    return extendedEnum().lookup(name);
  }

  public static ExtendedEnum<OvernightTermIndex> extendedEnum() {
    return ExtendedEnum.of(OvernightTermIndex.class);
  }

  @Override
  public boolean isActive() {
    return this.active;
  }

  @Override
  public FloatingRateName getFloatingRateName() {
    return FloatingRateName.of(this.floatingRateName);
  }

  public LocalDate calculateFixingFromEffective(LocalDate effectiveDate, ReferenceData refData) {
    HolidayCalendar calendar = fixingCalendar.resolve(refData);
    return calendar.shift(calendar.nextOrSame(effectiveDate), -fixingDateOffset.getDays());
  }
}
