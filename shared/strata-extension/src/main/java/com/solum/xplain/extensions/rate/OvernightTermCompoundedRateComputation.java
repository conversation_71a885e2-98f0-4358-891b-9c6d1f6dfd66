package com.solum.xplain.extensions.rate;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

@Value
@Builder
public class OvernightTermCompoundedRateComputation implements OvernightTermRateComputation {

  @NonNull OvernightTermIndex index;
  @NonNull HolidayCalendar fixingCalendar;
  @NonNull LocalDate startDate;
  @NonNull LocalDate endDate;
  int rateCutOffDays;

  public static OvernightTermCompoundedRateComputation of(
      OvernightTermIndex index,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      ReferenceData refData) {

    HolidayCalendar fixingCalendar = index.getFixingCalendar().resolve(refData);
    LocalDate adjustedStartDate = index.calculateFixingFromEffective(startDate, refData);
    LocalDate adjustedEndDate = index.calculateFixingFromEffective(endDate, refData);

    return OvernightTermCompoundedRateComputation.builder()
        .index(index)
        .fixingCalendar(fixingCalendar)
        .startDate(adjustedStartDate)
        .endDate(adjustedEndDate)
        .rateCutOffDays(rateCutOffDays) // cause there's not rate cut off days for term overnight
        .build();
  }
}
