package com.solum.xplain.extensions.rate;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.product.rate.RateComputation;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.Value;

/**
 * A rate computation for overnight term compounded rates.
 *
 * <p>This class represents a rate computation that uses an {@link OvernightTermIndex}
 * and includes a term first fixing date for proper term rate calculations.
 */
@Value
@EqualsAndHashCode
public class OvernightTermCompoundedRateComputation implements RateComputation, Serializable {

  @NonNull OvernightTermIndex index;
  @NonNull HolidayCalendar fixingCalendar;
  @NonNull LocalDate startDate;
  @NonNull LocalDate endDate;
  int rateCutOffDays;
  @NonNull LocalDate termFirstFixingDate;

  /**
   * Private constructor.
   */
  private OvernightTermCompoundedRateComputation(
      OvernightTermIndex index,
      HolidayCalendar fixingCalendar,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      LocalDate termFirstFixingDate) {
    this.index = index;
    this.fixingCalendar = fixingCalendar;
    this.startDate = startDate;
    this.endDate = endDate;
    this.rateCutOffDays = rateCutOffDays;
    this.termFirstFixingDate = termFirstFixingDate;
  }

  /**
   * Creates an OvernightTermCompoundedRateComputation with the specified parameters.
   *
   * @param index the overnight term index
   * @param startDate the start date of the accrual period
   * @param endDate the end date of the accrual period
   * @param rateCutOffDays the rate cut-off days
   * @param termFirstFixingDate the first fixing date for term rate calculation
   * @param refData the reference data
   * @return the overnight term compounded rate computation
   */
  public static OvernightTermCompoundedRateComputation of(
      OvernightTermIndex index,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      LocalDate termFirstFixingDate,
      ReferenceData refData) {

    HolidayCalendar fixingCalendar = index.getFixingCalendar().resolve(refData);

    return new OvernightTermCompoundedRateComputation(
        index,
        fixingCalendar,
        startDate,
        endDate,
        rateCutOffDays,
        termFirstFixingDate);
  }

  /**
   * Creates an OvernightTermCompoundedRateComputation from an existing one with a different term first fixing date.
   *
   * @param base the base computation to copy from
   * @param termFirstFixingDate the new first fixing date for term rate calculation
   * @return the overnight term compounded rate computation
   */
  public static OvernightTermCompoundedRateComputation of(
      OvernightTermCompoundedRateComputation base, LocalDate termFirstFixingDate) {
    return new OvernightTermCompoundedRateComputation(
        base.index,
        base.fixingCalendar,
        base.startDate,
        base.endDate,
        base.rateCutOffDays,
        termFirstFixingDate);
  }

  @Override
  public void collectIndices(ImmutableSet.Builder<Index> builder) {
    builder.add(index);
  }

  /**
   * Gets the underlying overnight index from the term index.
   * 
   * @return the overnight index
   */
  public OvernightIndex getOvernightIndex() {
    return OvernightIndex.of(index.getFloatingRateName().getName());
  }

  @Override
  public String toString() {
    return "OvernightTermCompoundedRateComputation{"
        + "index=" + index
        + ", startDate=" + startDate
        + ", endDate=" + endDate
        + ", rateCutOffDays=" + rateCutOffDays
        + ", termFirstFixingDate=" + termFirstFixingDate
        + '}';
  }
}
