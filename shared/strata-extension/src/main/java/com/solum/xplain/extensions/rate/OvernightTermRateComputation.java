package com.solum.xplain.extensions.rate;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.product.rate.RateComputation;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import java.util.Objects;

/** Computation of an overnight rate for a term index, similar to OvernightRateComputation. */
public interface OvernightTermRateComputation extends RateComputation {

  static OvernightTermRateComputation of(
      OvernightTermIndex index,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      OvernightAccrualMethod accrualMethod,
      ReferenceData referenceData) {
    if (Objects.requireNonNull(accrualMethod) == OvernightAccrualMethod.COMPOUNDED) {
      return OvernightTermCompoundedRateComputation.of(
          index, startDate, endDate, rateCutOffDays, referenceData);
    }
    throw new IllegalArgumentException(
        Messages.format("unsupported Overnight accrual method, {}", accrualMethod));
  }

  OvernightIndex getIndex();

  HolidayCalendar getFixingCalendar();

  LocalDate getStartDate();

  LocalDate getEndDate();

  default LocalDate calculatePublicationFromFixing(LocalDate fixingDate) {
    return this.getFixingCalendar()
        .shift(
            this.getFixingCalendar().nextOrSame(fixingDate),
            this.getIndex().getPublicationDateOffset());
  }

  default LocalDate calculateEffectiveFromFixing(LocalDate fixingDate) {
    return this.getFixingCalendar()
        .shift(
            this.getFixingCalendar().nextOrSame(fixingDate),
            this.getIndex().getEffectiveDateOffset());
  }

  default LocalDate calculateMaturityFromFixing(LocalDate fixingDate) {
    return this.getFixingCalendar()
        .shift(
            this.getFixingCalendar().nextOrSame(fixingDate),
            this.getIndex().getEffectiveDateOffset() + 1);
  }

  default LocalDate calculateFixingFromEffective(LocalDate effectiveDate) {
    return this.getFixingCalendar()
        .shift(
            this.getFixingCalendar().nextOrSame(effectiveDate),
            -this.getIndex().getEffectiveDateOffset());
  }

  default LocalDate calculateMaturityFromEffective(LocalDate effectiveDate) {
    return this.getFixingCalendar().shift(this.getFixingCalendar().nextOrSame(effectiveDate), 1);
  }

  default void collectIndices(ImmutableSet.Builder<Index> builder) {
    builder.add(this.getIndex());
  }
}
