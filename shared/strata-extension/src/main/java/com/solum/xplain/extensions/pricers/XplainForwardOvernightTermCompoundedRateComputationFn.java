/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.OvernightIndexRates;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.solum.xplain.extensions.rate.OvernightTermCompoundedRateComputation;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;

/**
 * Rate computation implementation for {@link OvernightTermCompoundedRateComputation}.
 * 
 * <p>This implementation delegates to the existing {@link XplainForwardOvernightCompoundedRateComputation}
 * by converting the term computation to a standard overnight compounded computation.
 */
@RequiredArgsConstructor
public class XplainForwardOvernightTermCompoundedRateComputationFn
    implements RateComputationFn<OvernightTermCompoundedRateComputation> {
  
  private final ReferenceData referenceData;

  @Override
  public double rate(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    // Convert to OvernightCompoundedRateComputation and delegate
    OvernightCompoundedRateComputation convertedComputation = convertToOvernightCompounded(computation);
    OvernightIndexRates rates = provider.overnightIndexRates(convertedComputation.getIndex());
    XplainForwardOvernightCompoundedRateComputation.ObservationDetails details =
        new XplainForwardOvernightCompoundedRateComputation.ObservationDetails(
            convertedComputation, startDate, endDate, rates, referenceData);
    return details.calculateRate();
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    
    // Convert to OvernightCompoundedRateComputation and delegate
    OvernightCompoundedRateComputation convertedComputation = convertToOvernightCompounded(computation);
    OvernightIndexRates rates = provider.overnightIndexRates(convertedComputation.getIndex());
    XplainForwardOvernightCompoundedRateComputation.ObservationDetails details =
        new XplainForwardOvernightCompoundedRateComputation.ObservationDetails(
            convertedComputation, startDate, endDate, rates, referenceData);
    return details.calculateRateSensitivity();
  }

  @Override
  public double explainRate(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }

  private OvernightCompoundedRateComputation convertToOvernightCompounded(OvernightTermCompoundedRateComputation termComputation) {
    return OvernightCompoundedRateComputation.of(
        termComputation.getIndex().getOvernightIndex(),
        termComputation.getStartDate(),
        termComputation.getEndDate(),
        termComputation.getRateCutOffDays(),
        referenceData);
  }
}
