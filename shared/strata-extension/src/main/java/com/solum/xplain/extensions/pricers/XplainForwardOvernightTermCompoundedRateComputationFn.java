/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.solum.xplain.extensions.rate.OvernightTermCompoundedRateComputation;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;

/**
 * Rate computation implementation for {@link OvernightTermCompoundedRateComputation}.
 * 
 * <p>This implementation delegates to the existing {@link XplainForwardOvernightCompoundedRateComputation}
 * by converting the term computation to a standard overnight compounded computation.
 */
@RequiredArgsConstructor
public class XplainForwardOvernightTermCompoundedRateComputationFn
    implements RateComputationFn<OvernightTermCompoundedRateComputation> {
  
  private final XplainForwardOvernightCompoundedRateComputation delegate;

  public XplainForwardOvernightTermCompoundedRateComputationFn(ReferenceData referenceData) {
    this.delegate = new XplainForwardOvernightCompoundedRateComputation(referenceData);
  }

  @Override
  public double rate(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    // Convert to OvernightCompoundedRateComputation and delegate
    OvernightCompoundedRateComputation convertedComputation = convertToOvernightCompounded(computation);
    return delegate.rate(convertedComputation, startDate, endDate, provider, computation.getTermFirstFixingDate());
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    
    // Convert to OvernightCompoundedRateComputation and delegate
    OvernightCompoundedRateComputation convertedComputation = convertToOvernightCompounded(computation);
    var rates = provider.overnightIndexRates(convertedComputation.getIndex());
    
    return delegate.rateSensitivity(
        convertedComputation,
        startDate,
        endDate,
        rates,
        delegate.getReferenceData(),
        true, // isOvernightTerm = true
        computation.getTermFirstFixingDate());
  }

  @Override
  public double explainRate(
      OvernightTermCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }

  private OvernightCompoundedRateComputation convertToOvernightCompounded(OvernightTermCompoundedRateComputation termComputation) {
    // Convert OvernightTermIndex to OvernightIndex using the floating rate name
    OvernightIndex overnightIndex = OvernightIndex.of(termComputation.getIndex().getFloatingRateName().getName());
    
    return OvernightCompoundedRateComputation.of(
        overnightIndex,
        termComputation.getStartDate(),
        termComputation.getEndDate(),
        termComputation.getRateCutOffDays(),
        delegate.getReferenceData());
  }

  public ReferenceData getReferenceData() {
    return delegate.getReferenceData();
  }
}
