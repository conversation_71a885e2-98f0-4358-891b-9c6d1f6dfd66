/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.impl.rate.DispatchingRateComputationFn;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.RateComputation;
import com.solum.xplain.extensions.index.OvernightTermRateComputation;
import com.solum.xplain.extensions.rate.OvernightTermCompoundedRateComputation;
import java.time.LocalDate;

/**
 * Extended dispatching rate computation function that can handle both standard Strata rate computations
 * and custom {@link OvernightTermCompoundedRateComputation} and {@link OvernightTermRateComputation}.
 *
 * <p>This class delegates to the standard {@link DispatchingRateComputationFn} for all standard types
 * and handles custom overnight term computations separately.
 */
public class XplainDispatchingRateComputationFn implements RateComputationFn<RateComputation> {
  
  private final DispatchingRateComputationFn standardDispatcher;
  private final XplainForwardOvernightTermCompoundedRateComputationFn termOvernightCompoundedFn;
  private final XplainOvernightTermRateComputationFn termOvernightFn;

  public XplainDispatchingRateComputationFn(
      DispatchingRateComputationFn standardDispatcher,
      ReferenceData referenceData) {
    this.standardDispatcher = standardDispatcher;
    this.termOvernightCompoundedFn = new XplainForwardOvernightTermCompoundedRateComputationFn(referenceData);
    this.termOvernightFn = new XplainOvernightTermRateComputationFn(referenceData);
  }

  @Override
  public double rate(
      RateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    if (computation instanceof OvernightTermCompoundedRateComputation) {
      return termOvernightCompoundedFn.rate((OvernightTermCompoundedRateComputation) computation, startDate, endDate, provider);
    } else if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.rate((OvernightTermRateComputation) computation, startDate, endDate, provider);
    } else {
      return standardDispatcher.rate(computation, startDate, endDate, provider);
    }
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      RateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    
    if (computation instanceof OvernightTermCompoundedRateComputation) {
      return termOvernightCompoundedFn.rateSensitivity((OvernightTermCompoundedRateComputation) computation, startDate, endDate, provider);
    } else if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.rateSensitivity((OvernightTermRateComputation) computation, startDate, endDate, provider);
    } else {
      return standardDispatcher.rateSensitivity(computation, startDate, endDate, provider);
    }
  }

  @Override
  public double explainRate(
      RateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    if (computation instanceof OvernightTermCompoundedRateComputation) {
      return termOvernightCompoundedFn.explainRate((OvernightTermCompoundedRateComputation) computation, startDate, endDate, provider, builder);
    } else if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.explainRate((OvernightTermRateComputation) computation, startDate, endDate, provider, builder);
    } else {
      return standardDispatcher.explainRate(computation, startDate, endDate, provider, builder);
    }
  }
}
