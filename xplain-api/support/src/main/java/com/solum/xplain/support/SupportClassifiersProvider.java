package com.solum.xplain.support;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.support.setup.SetupProperties;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SupportClassifiersProvider implements ClassifiersProvider {
  public static final String RESET_DATASETS = "resetDatasets";
  private final SetupProperties setupProperties;

  @Override
  public List<Classifier> classifiers() {
    return List.of(resetDataset());
  }

  @Override
  public int sortOrder() {
    return 8;
  }

  private Classifier resetDataset() {
    return new Classifier(
        RESET_DATASETS,
        setupProperties.getReset().getDatasets().stream().map(Classifier::new).toList());
  }
}
