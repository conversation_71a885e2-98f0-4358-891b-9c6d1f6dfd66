package com.solum.xplain.support;

import com.solum.xplain.core.CoreConfig;
import com.solum.xplain.support.retention.value.RetentionProperties;
import com.solum.xplain.support.setup.SetupProperties;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

@Configuration
@EnableAsync
@Import({CoreConfig.class})
@EnableConfigurationProperties({RetentionProperties.class, SetupProperties.class})
@ComponentScan(
    excludeFilters = {
      @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
      @Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class)
    })
@EntityScan(basePackages = {"com.solum.xplain.support"})
public class SupportConfig {}
