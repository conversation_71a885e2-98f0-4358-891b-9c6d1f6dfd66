package com.solum.xplain.support.permissions;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SupportAuthorities {

  public static final String RUN_DB_CLEANUP = "R_DB_CLEAN";
  public static final String RUN_DB_RESET = "R_DB_RESET";
  public static final String VIEW_LOCK_STATE = "V_LOCKS";

  private static final String PREFIX = "hasAuthority('";
  private static final String SUFFIX = "')";

  public static final String AUTHORITY_RUN_DB_CLEANUP = PREFIX + RUN_DB_CLEANUP + SUFFIX;
  public static final String AUTHORITY_RUN_DB_RESET = PREFIX + RUN_DB_RESET + SUFFIX;
  public static final String AUTHORITY_VIEW_LOCK_STATE = PREFIX + VIEW_LOCK_STATE + SUFFIX;
}
