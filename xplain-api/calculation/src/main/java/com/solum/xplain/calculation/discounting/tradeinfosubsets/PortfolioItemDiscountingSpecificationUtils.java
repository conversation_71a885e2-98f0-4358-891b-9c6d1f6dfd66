package com.solum.xplain.calculation.discounting.tradeinfosubsets;

import static com.google.common.math.DoubleMath.fuzzyEquals;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_PAIRS;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.extensions.index.OffshoreIndices;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

public final class PortfolioItemDiscountingSpecificationUtils {

  public static final String INDEX_NOT_FOUND = "Unable to resolve discount index";

  public static Optional<FloatingRateIndex> index(CalculationType type, String indexName) {
    if (type == null) {
      return Optional.empty();
    }
    return switch (type) {
      case FIXED -> Optional.empty();
      case IBOR -> Optional.of(IborIndex.of(indexName));
      case OVERNIGHT -> Optional.of(OvernightIndex.of(indexName));
      case INFLATION -> Optional.of(PriceIndex.of(indexName));
      case TERM_OVERNIGHT -> Optional.of(OvernightTermIndex.of(indexName));
    };
  }

  public static Optional<FloatingRateIndex> rateIndex(
      CalculationType type, String indexName, boolean isOffshore) {
    return index(type, indexName).map(i -> toOffshoreIndex(isOffshore, i));
  }

  private static FloatingRateIndex toOffshoreIndex(boolean isOffshore, FloatingRateIndex index) {
    if (!isOffshore) {
      return index;
    }

    if (index instanceof IborIndex iborIndex) {
      return OffshoreIndices.lookupOffshoreIbor(iborIndex).orElseThrow();
    } else if (index instanceof OvernightIndex overnightIndex) {
      return OffshoreIndices.lookupOffshoreOvernight(overnightIndex).orElseThrow();
    } else {
      return index;
    }
  }

  public static FloatingRateIndex swapIndex(
      SwapIndexLeg leg1, SwapIndexLeg leg2, boolean ignoreSpread) {

    return Stream.of(leg1, leg2)
        .filter(leg -> isFloatingRateIndex(leg.type))
        .filter(leg -> ignoreSpread || withoutLegSpread(leg.spread))
        .map(leg -> rateIndex(leg.type, leg.indexName, leg.isOffshore))
        .flatMap(Optional::stream)
        .findAny()
        .orElse(null);
  }

  private static boolean isFloatingRateIndex(CalculationType type) {
    return type == CalculationType.IBOR
        || type == CalculationType.OVERNIGHT
        || type == CalculationType.TERM_OVERNIGHT;
  }

  private static boolean withoutLegSpread(Double legSpread) {
    return Objects.isNull(legSpread) || fuzzyEquals(legSpread, 0, 1e-6);
  }

  /**
   * Returns a currency pair (as defined in {@link
   * com.solum.xplain.extensions.constants.PermissibleCurrencies#FX_SWAP_PAIRS}) for the given
   * currency codes. If the pair is not found, it returns the conventional pair.
   *
   * @param ccyCode1 first currency code
   * @param ccyCode2 second currency code
   */
  public static CurrencyPair fxCurrencyPair(String ccyCode1, String ccyCode2) {
    if (ccyCode1 == null || ccyCode2 == null) {
      return null;
    }
    var ccy1 = Currency.of(ccyCode1);
    var ccy2 = Currency.of(ccyCode2);

    return FX_SWAP_PAIRS.stream()
        .filter(cp -> cp.contains(ccy1) && cp.contains(ccy2))
        .findAny()
        .orElseGet(() -> CurrencyPair.of(ccy1, ccy2).toConventional());
  }

  public record SwapIndexLeg(
      CalculationType type, String indexName, boolean isOffshore, Double spread) {}
}
