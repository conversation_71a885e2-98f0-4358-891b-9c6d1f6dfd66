package com.solum.xplain.calculation

import com.solum.xplain.calibration.rates.charts.CalibratedCurve
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = CalculationResultCurves)
class CalculationResultCurvesBuilder {
  CalculationResultCurvesBuilder() {
    curveConfigurationCurves([])
    fxCurveConfigurationCurves([])
  }

  static def calculationResultCurves(ObjectId calculationResultId, List<CalibratedCurve> curveConfigurationChartData = [], List<CalibratedCurve> fxCurveConfigurationChartData = []) {
    new CalculationResultCurvesBuilder()
      .id(calculationResultId)
      .curveConfigurationCurves(curveConfigurationChartData)
      .fxCurveConfigurationCurves(fxCurveConfigurationChartData)
      .build()
  }
}
