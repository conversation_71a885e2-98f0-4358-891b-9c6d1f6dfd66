package com.solum.xplain.calculation.integration

import com.solum.xplain.calculation.CalculationPortfolioItem
import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.exposure.HedgeProfile
import com.solum.xplain.calculation.exposure.schedule.CcyExposureScheduleFrequency
import com.solum.xplain.calculation.exposure.simulation.NetExposureSimulationShiftResults
import com.solum.xplain.calculation.exposure.simulation.SimulationCalculationOptions
import com.solum.xplain.calculation.exposure.simulation.SimulationExposureCalculationService
import com.solum.xplain.calculation.repository.CalculationPortfolioItemRepository
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureCalculationKey
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType
import com.solum.xplain.calculation.value.CalculationResultTotalsForm
import com.solum.xplain.calculation.value.Metrics
import com.solum.xplain.core.ccyexposure.entity.Cashflow
import com.solum.xplain.core.ccyexposure.entity.CcyExposure
import com.solum.xplain.core.ccyexposure.repository.CcyExposureCashflowRepository
import com.solum.xplain.core.ccyexposure.value.CashflowForm
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.HedgeTradeDetails
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.extensions.enums.CallPutType
import com.solum.xplain.extensions.enums.PositionType
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class SimulationExposureCalculationServiceTest extends IntegrationSpecification {

  static def SIMULATION_ID = "000000000000000000000001"
  static def STATE_DATE = LocalDate.parse("2024-05-15")
  static def EXPOSURE_CURRENCY = "USD"

  @Resource
  SimulationExposureCalculationService service

  @Resource
  CalculationPortfolioItemRepository calculationPortfolioItemRepository

  @Resource
  CalculationResultRepository calculationResultRepository

  @Resource
  CcyExposureCashflowRepository cashflowRepository

  @Resource
  MongoOperations operations

  def cleanup() {
    operations.dropCollection(CalculationResult.class)
    operations.dropCollection(Cashflow.class)
    operations.dropCollection(CcyExposure.class)
    operations.dropCollection(CalculationPortfolioItem.class)
  }

  def "should calculate exposure for simulation"() {
    setup:
    def tableFilter = TableFilter.emptyTableFilter()
    def totals = new CalculationResultTotalsForm(all: true)

    def baseCalculationResult = calculationResult(0.0d, null)
    def calcResult1 = calculationResult(0.1d)
    def calcResult2 = calculationResult(0.2d)
    operations.insert(baseCalculationResult)
    operations.insert(calcResult1)
    operations.insert(calcResult2)

    // fwds
    operations.insert(fwd(baseCalculationResult.id, STATE_DATE.plusWeeks(1), 25d))
    operations.insert(fwd(calcResult1.id, STATE_DATE.plusWeeks(1), 25d))
    operations.insert(fwd(baseCalculationResult.id, STATE_DATE.plusWeeks(3), 25d))
    operations.insert(fwd(calcResult1.id, STATE_DATE.plusWeeks(5), 25d))
    operations.insert(fwd(calcResult2.id, STATE_DATE.plusWeeks(1), 25d))
    operations.insert(fwd(calcResult2.id, STATE_DATE.plusYears(2), 25d))

    // fxos
    operations.insert(fxo(baseCalculationResult.id, STATE_DATE.plusWeeks(1), 1.5d, 50d))
    operations.insert(fxo(calcResult1.id, STATE_DATE.plusWeeks(5), 7.5d, 50d))
    operations.insert(fxo(calcResult2.id, STATE_DATE.plusWeeks(3), 1.5d, 50d))
    operations.insert(fxo(baseCalculationResult.id, STATE_DATE.plusWeeks(3), 1.5d, 50d))
    operations.insert(fxo(baseCalculationResult.id, STATE_DATE.plusMonths(2), 7.5d, 50d))
    operations.insert(fxo(baseCalculationResult.id, STATE_DATE.minusYears(1), 10.5d, 50d))

    // external ccy exposures
    def externalCcyExposure1 = ccyExposure()
    operations.insert(externalCcyExposure1)
    cashflowRepository.create(CashflowForm.newOfWithDefaultVersion(externalCcyExposure1.id, STATE_DATE.plusWeeks(1), 1000d))
    cashflowRepository.create(CashflowForm.newOfWithDefaultVersion(externalCcyExposure1.id, STATE_DATE.plusWeeks(3), 2000d))
    def externalCcyExposure2 = ccyExposure()
    operations.insert(externalCcyExposure2)
    cashflowRepository.create(CashflowForm.newOfWithDefaultVersion(externalCcyExposure2.id, STATE_DATE.plusYears(1), 1000d))
    cashflowRepository.create(CashflowForm.newOfWithDefaultVersion(externalCcyExposure2.id, STATE_DATE.plusWeeks(3), 2050d))

    def options = simulationCalculationOptions(
      ShiftDataType.SPOT,
      [externalCcyExposure1.id, externalCcyExposure2.id],
      baseCalculationResult.id,
      STATE_DATE.minusDays(5),
      STATE_DATE.plusMonths(6)
      )

    when:
    def result = service.calculate(
      SIMULATION_ID,
      options,
      tableFilter,
      totals
      )

    then:
    result != null
    def results = (List<NetExposureSimulationShiftResults>) result

    def baseResult = results.get(0)
    baseResult.shiftSize() == 0.0
    def baseMetrics = baseResult.metricsList()
    baseMetrics.size() == 7
    baseMetrics.get(1).baseCalculationDeltaNetExposure == null
    baseMetrics.get(1).deltaNetExposureAfterShift == null
    baseMetrics.get(1).baseCalculationDeltaHedgeRatio == null
    baseMetrics.get(1).deltaHedgeRatioAfterShift == null
    baseMetrics.get(1).baseCalculationPresentValue == null
    baseMetrics.get(1).presentValueAfterShift == null
    baseMetrics.get(1).scheduleDate == LocalDate.parse("2024-06-15")
    baseMetrics.get(1).exposure == 5050.0
    baseMetrics.get(1).forwardsExposure == 2000.0
    baseMetrics.get(1).optionExposure == 2000.0
    baseMetrics.get(1).netExposure == 9050.0
    baseMetrics.get(1).hedgeRatio == 0.7920792079207921
    baseMetrics.get(1).deltaOptions == 6.0
    baseMetrics.get(1).deltaNetExposure == 7056.0
    baseMetrics.get(1).deltaHedgeRatio == 0.39722772277227725
    baseMetrics.get(1).presentValue == 150.0

    def shift1Result = results.get(1)
    shift1Result.shiftSize() == 0.1
    def shift1ResultMetrics = shift1Result.metricsList()
    shift1ResultMetrics.size() == 7
    shift1ResultMetrics.get(1).baseCalculationDeltaNetExposure == 7056.0
    shift1ResultMetrics.get(1).deltaNetExposureAfterShift == -1006.0
    shift1ResultMetrics.get(1).baseCalculationDeltaHedgeRatio == 0.39722772277227725
    shift1ResultMetrics.get(1).deltaHedgeRatioAfterShift == -0.19920792079207922
    shift1ResultMetrics.get(1).baseCalculationPresentValue == 150.0
    shift1ResultMetrics.get(1).presentValueAfterShift == -125.0
    shift1ResultMetrics.get(1).scheduleDate == LocalDate.parse("2024-06-15")
    shift1ResultMetrics.get(1).exposure == 5050.0
    shift1ResultMetrics.get(1).forwardsExposure == 1000.0
    shift1ResultMetrics.get(1).optionExposure == 0.0
    shift1ResultMetrics.get(1).netExposure == 6050.0
    shift1ResultMetrics.get(1).hedgeRatio == 0.19801980198019803
    shift1ResultMetrics.get(1).deltaOptions == 0.0
    shift1ResultMetrics.get(1).deltaNetExposure == 6050.0
    shift1ResultMetrics.get(1).deltaHedgeRatio == 0.19801980198019803
    shift1ResultMetrics.get(1).presentValue == 25.0

    def shift2Result = results.get(2)
    shift2Result.shiftSize() == 0.2
    def shift2ResultMetrics = shift2Result.metricsList()
    shift2ResultMetrics.size() == 7
    shift2ResultMetrics.get(1).baseCalculationDeltaNetExposure == 7056.0
    shift2ResultMetrics.get(1).deltaNetExposureAfterShift == -1003
    shift2ResultMetrics.get(1).baseCalculationDeltaHedgeRatio == 0.39722772277227725
    shift2ResultMetrics.get(1).deltaHedgeRatioAfterShift == -0.19861386138613862
    shift2ResultMetrics.get(1).baseCalculationPresentValue == 150.0
    shift2ResultMetrics.get(1).presentValueAfterShift == -75.0
    shift2ResultMetrics.get(1).scheduleDate == LocalDate.parse("2024-06-15")
    shift2ResultMetrics.get(1).exposure == 5050.0
    shift2ResultMetrics.get(1).forwardsExposure == 1000.0
    shift2ResultMetrics.get(1).optionExposure == 1000.0
    shift2ResultMetrics.get(1).netExposure == 7050.0
    shift2ResultMetrics.get(1).hedgeRatio == 0.39603960396039606
    shift2ResultMetrics.get(1).deltaOptions == 3.0
    shift2ResultMetrics.get(1).deltaNetExposure == 6053.0
    shift2ResultMetrics.get(1).deltaHedgeRatio == 0.19861386138613862
    shift2ResultMetrics.get(1).presentValue == 75.0
  }

  def simulationCalculationOptions(ShiftDataType shiftDataType, List<String> exposureIds, ObjectId baseCalculationResultId, LocalDate horizonStart, LocalDate horizonEnd) {
    new SimulationCalculationOptions(
      calculationResultId: baseCalculationResultId,
      shiftDataType: shiftDataType,
      stateDate: STATE_DATE,
      exposureIds: exposureIds,
      exposureCurrency: EXPOSURE_CURRENCY,
      horizonStartDate: horizonStart,
      horizonEndDate: horizonEnd,
      hedgeProfile: HedgeProfile.EXECUTED_ONLY,
      ccyExposureFrequency: CcyExposureScheduleFrequency.MONTHLY
      )
  }

  def fwd(ObjectId calcResultId, LocalDate endDate, Double presentValue) {
    def payLegDetails = new TradeLegDetails()
    payLegDetails.setCurrency("USD")
    payLegDetails.setNotional(1000d)

    def recLegDetails = new TradeLegDetails()
    recLegDetails.setCurrency("EUR")
    recLegDetails.setNotional(500d)

    def details = new TradeDetails()
    details.setEndDate(endDate)
    details.setPayLeg(payLegDetails)
    details.setReceiveLeg(recLegDetails)
    details.setPositionType(PositionType.BUY)
    def hd = new HedgeTradeDetails()
    hd.setPotentialHedge(false)
    details.setHedgeTradeDetails(hd)

    def metrics = new Metrics()
    metrics.setLocalCcy("USD")
    metrics.setPresentValue(presentValue)

    new CalculationPortfolioItem(
      calculationResultId: calcResultId,
      productType: CoreProductType.FXFWD,
      tradeDetails: details,
      tradeId: ObjectId.get().toString(),
      description: "Executed",
      metrics: metrics
      )
  }

  def fxo(ObjectId calcResultId, LocalDate endDate, Double deltaFwd, Double presentValue) {
    def payLegDetails = new TradeLegDetails()
    payLegDetails.setCurrency("USD")
    payLegDetails.setNotional(1000d)

    def recLegDetails = new TradeLegDetails()
    recLegDetails.setCurrency("EUR")
    recLegDetails.setNotional(500d)

    def details = new TradeDetails()
    details.setEndDate(endDate)
    details.setPayLeg(payLegDetails)
    details.setReceiveLeg(recLegDetails)
    details.setPositionType(PositionType.BUY)
    def hd = new HedgeTradeDetails()
    hd.setPotentialHedge(false)
    details.setHedgeTradeDetails(hd)

    def optionDetails = new OptionTradeDetails()
    optionDetails.setCallPutType(CallPutType.PUT)
    details.setOptionTradeDetails(optionDetails)

    def metrics = new Metrics()
    metrics.setLocalCcy("USD")
    metrics.setDeltaFwdLocalCcy(deltaFwd)
    metrics.setDeltaFwd(deltaFwd)
    metrics.setPresentValue(presentValue)
    metrics.setFxSpot(1.0d)

    new CalculationPortfolioItem(
      calculationResultId: calcResultId,
      productType: CoreProductType.FXOPT,
      tradeDetails: details,
      tradeId: ObjectId.get().toString(),
      metrics: metrics,
      description: "Executed"
      )
  }

  def calculationResult(Double shiftSize, ShiftDataType shiftDataType = ShiftDataType.SPOT) {
    def exposureKey = new CcyExposureCalculationKey(
      SIMULATION_ID,
      shiftDataType,
      shiftSize
      )

    new CalculationResult(
      ccyExposureCalculationKey: exposureKey
      )
  }

  def ccyExposure(String exposureCurrency = EXPOSURE_CURRENCY) {
    def entity = new CcyExposure("name", exposureCurrency)
    entity.setId(ObjectId.get().toString())
    entity
  }

  def cashflow(String ccyExposureId, LocalDate date, Double amount) {
    def entity = Cashflow.newOf()
    entity.ccyExposureId = ccyExposureId
    entity.date = date
    entity.amount = amount
    entity
  }
}
