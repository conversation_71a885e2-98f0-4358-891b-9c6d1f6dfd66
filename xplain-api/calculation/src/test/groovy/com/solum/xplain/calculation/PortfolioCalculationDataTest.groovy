package com.solum.xplain.calculation

import com.opengamma.strata.basics.ImmutableReferenceData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.value.CalculationOptions
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class PortfolioCalculationDataTest extends Specification {

  @Shared
  def portfolio = Mock(PortfolioCondensedView)
  @Shared
  def options = Mock(CalculationOptions)
  @Shared
  def marketData = Mock(CalculationResultMarketData)
  @Shared
  def calibrationResults = Mock(CalculationCurveGroupData)
  @Shared
  def fxCalibrationResults = Mock(CalculationCurveGroupData)
  @Shared
  def calculationTrades = Mock(CalculationTrades)
  @Shared
  def referenceData = ImmutableReferenceData.empty()

  def "should build correct portfolio calculation data"() {
    when:
    def result = samplePortfolioCalculation()

    then:
    result.getCalculationId() != null
    result.getPortfolio() == portfolio
    result.getMarketData() == marketData
    result.getReferenceData() == referenceData
    result.getOptions() == options
    result.getCalibrationResult() == calibrationResults
    result.getFxCalibrationResult() == fxCalibrationResults
    result.getCalculationTrades() == calculationTrades
    result.fxCalibrationResult() == Optional.of(fxCalibrationResults)
  }

  @Unroll
  def "should resolve calibration result when #type #result"() {
    setup:
    def portfolioCalculationData = samplePortfolioCalculation()

    expect:
    portfolioCalculationData.result(type) == result

    where:
    type              | result
    CoreProductType.IRS   | calibrationResults
    CoreProductType.FXFWD | fxCalibrationResults
  }

  private samplePortfolioCalculation() {
    PortfolioCalculationData.builder()
      .portfolio(portfolio)
      .options(options)
      .marketData(marketData)
      .referenceData(referenceData)
      .calibrationResult(calibrationResults)
      .fxCalibrationResult(fxCalibrationResults)
      .calculationTrades(calculationTrades)
      .build()
  }
}
