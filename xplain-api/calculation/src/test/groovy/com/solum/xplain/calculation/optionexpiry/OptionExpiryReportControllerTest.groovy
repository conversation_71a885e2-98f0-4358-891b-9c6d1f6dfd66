package com.solum.xplain.calculation.optionexpiry

import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_BUY
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.helpers.MockMvcConfiguration
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportParams
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.shared.utils.filter.TableFilter
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@MockMvcConfiguration
@WebMvcTest(controllers = [OptionExpiryReportController])
class OptionExpiryReportControllerTest extends Specification {
  def static CALC_ID = ObjectId.get().toHexString()

  @SpringBean
  OptionExpiryReportControllerService service = Mock()

  @Autowired
  ObjectMapper mapper

  @Autowired
  MockMvc mockMvc

  def "should get option expiry reports"() {
    when:
    service.report(_ as Authentication, CALC_ID, _ as TableFilter, new OptionExpiryReportParams(null, null, null)) >> right([])
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/option-expiry', CALC_ID)
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString() == "[]"
    }

    when:
    service.report(_ as Authentication, CALC_ID, _ as TableFilter, new OptionExpiryReportParams([SWAPTION_PAY_BUY], BigDecimal.valueOf(0.5), 5)) >> right([])
    def resultsWithParams = mockMvc.perform(get('/calculation-result/{calculationResultId}/option-expiry', CALC_ID)
      .param(OptionExpiryReportParams.Fields.types, SWAPTION_PAY_BUY.name())
      .param(OptionExpiryReportParams.Fields.expiryStepSize, "0.5")
      .param(OptionExpiryReportParams.Fields.itmStepCount, "5")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(resultsWithParams.getResponse()) {
      getStatus() == 200
      getContentAsString() == "[]"
    }
  }

  def "should get  option expiry reports in csv"() {
    setup:
    service.reportsCsv(_ as Authentication, CALC_ID, _ as TableFilter, new OptionExpiryReportParams(null, null, null)) >>
      right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/option-expiry/csv', CALC_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }
}
