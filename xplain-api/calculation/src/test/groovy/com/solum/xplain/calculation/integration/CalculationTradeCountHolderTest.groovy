package com.solum.xplain.calculation.integration


import com.solum.xplain.shared.datagrid.AtomicCounter
import com.solum.xplain.shared.datagrid.DataGrid
import org.bson.types.ObjectId
import spock.lang.Specification

class CalculationTradeCountHolderTest extends Specification {
  static CALCULATION_ID = ObjectId.get().toHexString()
  static CALCULATION_ERR_ID = CALCULATION_ID + "-err"
  static INITIAL_TRADE_COUNT = 27

  DataGrid dataGrid = Mock()
  CalculationTradeCountHolder countHolder = new CalculationTradeCountHolder(dataGrid)
  AtomicCounter tradeCounter = Mock(AtomicCounter)
  AtomicCounter failureCounter = Mock(AtomicCounter)

  def "should init with trades count"() {
    setup:
    mockDataGrid()

    when:
    countHolder.registerCalculation(CALCULATION_ID, INITIAL_TRADE_COUNT)

    then:
    1 * tradeCounter.set(INITIAL_TRADE_COUNT)
    1 * failureCounter.set(0l)
  }

  def "should update and return false if no new responses"() {
    setup:
    mockDataGrid()

    when:
    def result = countHolder.allTradesProcessed(CALCULATION_ID, 0)

    then:
    !result
  }

  def "should update and return if all trades processed"() {
    setup:
    mockDataGrid()

    when:
    def result = countHolder.allTradesProcessed(CALCULATION_ID, 25)

    then:
    result == expected

    1 * tradeCounter.decrementAndGet(25l) >> unprocessed

    where:
    unprocessed | expected
    0           | true
    -1          | true
    1           | false
  }

  def "should update failure count"() {
    setup:
    mockDataGrid()

    when:
    def failureCount = countHolder.updateFailureCount(CALCULATION_ID, 5)

    then:
    failureCount == 10l
    1 * failureCounter.incrementAndGet(5l) >> 10l
  }

  def "should not throw error if trade counter not found"() {
    setup:
    1 * dataGrid.getAtomicCounter(CALCULATION_ID) >> { throw new IllegalArgumentException() }
    expect:
    countHolder.allTradesProcessed(CALCULATION_ID, 1)
  }

  def "should not throw error if failure counter not found"() {
    setup:
    1 * dataGrid.getAtomicCounter(CALCULATION_ERR_ID) >> { throw new IllegalArgumentException() }
    expect:
    countHolder.updateFailureCount(CALCULATION_ID, 1) == 0l
  }

  def "should not throw error if trade counter destroyed on other thread"() {
    setup:
    def atomicCounter = Mock(AtomicCounter)
    1 * dataGrid.getAtomicCounter(CALCULATION_ID) >> atomicCounter
    1 * atomicCounter.decrementAndGet(1) >> { throw new IllegalArgumentException() }
    expect:
    countHolder.allTradesProcessed(CALCULATION_ID, 1)
  }

  def "should not throw error if failure counter destroyed on other thread"() {
    setup:
    def atomicCounter = Mock(AtomicCounter)
    1 * dataGrid.getAtomicCounter(CALCULATION_ERR_ID) >> atomicCounter
    1 * atomicCounter.incrementAndGet(1) >> { throw new IllegalArgumentException() }
    expect:
    countHolder.updateFailureCount(CALCULATION_ID, 1) == 0l
  }

  def "should remove calculation"() {
    setup:
    mockDataGrid()

    when:
    countHolder.removeCalculation(CALCULATION_ID)

    then:
    1 * tradeCounter.closeQuietly()
    1 * failureCounter.closeQuietly()
  }

  def "should get remaining trades"() {
    setup:
    mockDataGrid()
    1 * tradeCounter.get() >> 1L

    when:
    def result = countHolder.remainingUnprocessedTrades(CALCULATION_ID)

    then:
    result == 1L
  }

  def "should get failure count"() {
    setup:
    mockDataGrid()
    1 * failureCounter.get() >> 136L

    when:
    def result = countHolder.failedProcessedTrades(CALCULATION_ID)

    then:
    result == 136L
  }

  def "should not throw error calculating remaining trades when counter destroyed"() {
    setup:
    mockDataGrid()
    1 * tradeCounter.get() >> { throw new IllegalArgumentException() }

    when:
    def result = countHolder.remainingUnprocessedTrades(CALCULATION_ID)

    then:
    result == 0L
  }

  def "should not throw error calculating failure count when counter destroyed"() {
    setup:
    mockDataGrid()
    1 * failureCounter.get() >> { throw new IllegalArgumentException() }

    when:
    def result = countHolder.failedProcessedTrades(CALCULATION_ID)

    then:
    result == 0L
  }

  def mockDataGrid() {
    (_..1) * dataGrid.getAtomicCounter(CALCULATION_ID) >> tradeCounter
    (_..1) * dataGrid.getAtomicCounter(CALCULATION_ERR_ID) >> failureCounter
  }
}
