package com.solum.xplain.calculation

import static com.solum.xplain.calculation.CalculationResultStatus.SAVED
import static com.solum.xplain.calculation.CalculationResultStatus.UNSAVED
import static com.solum.xplain.core.portfolio.CalculationStatus.FINISHED

import com.solum.xplain.calculation.events.TradesCalculationFinishedEvent
import com.solum.xplain.calculation.events.TradesProcessedEvent
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.listeners.CalculationProcessedEventListener
import com.solum.xplain.calculation.repository.CalculationResultRepository
import io.atlassian.fugue.Either
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class CalculationProcessedEventListenerTest extends Specification {
  def static CALCULATION_ID = ObjectId.get()
  def static PORTFOLIO_ID = ObjectId.get()
  def static ONBOARDING_ID = ObjectId.get()

  CalculationResultRepository calculationResultRepository = Mock()
  ApplicationEventPublisher eventPublisher = Mock()
  CalibrationCacheService cacheService = Mock()
  CalculationTradeCountHolder calculationTradeCountHolder = Mock()

  CalculationProcessedEventListener finalizer = new CalculationProcessedEventListener(
  calculationResultRepository,
  eventPublisher,
  cacheService,
  calculationTradeCountHolder)

  def "should finish dashboard calculation"() {
    setup:
    def dashboardResult = new CalculationResult(
      id: CALCULATION_ID,
      portfolioId: PORTFOLIO_ID,
      dashboardId: "dashboardId",
      calculationResultStatus: CalculationResultStatus.IN_PROGRESS,
      tradesStatistics: CalculationTradesStatistics.newOf(1l)
      )

    when:
    finalizer.onCalculationProcessed(TradesProcessedEvent.newOf(CALCULATION_ID.toHexString()))

    then:
    1 * eventPublisher.publishEvent(TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, "dashboardId", null, null, FINISHED))
    1 * calculationResultRepository.calculationEntity(CALCULATION_ID.toString()) >> Either.right(dashboardResult)
    1 * calculationTradeCountHolder.failedProcessedTrades(CALCULATION_ID.toString()) >> 0d
    1 * cacheService.cleanCalculationCache(CALCULATION_ID.toString())
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID.toString())
    1 * calculationResultRepository.finishCalculation(CALCULATION_ID, 0d, SAVED)
  }

  def "should finish onboarding report calculation"() {
    setup:
    def onboardingResult = new CalculationResult(
      id: CALCULATION_ID,
      portfolioId: PORTFOLIO_ID,
      onboardingReportId: ONBOARDING_ID,
      calculationResultStatus: CalculationResultStatus.IN_PROGRESS,
      tradesStatistics: CalculationTradesStatistics.newOf(1l)
      )

    when:
    finalizer.onCalculationProcessed(TradesProcessedEvent.newOf(CALCULATION_ID.toHexString()))

    then:
    1 * eventPublisher.publishEvent(TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, null, ONBOARDING_ID, FINISHED))
    1 * calculationResultRepository.calculationEntity(CALCULATION_ID.toString()) >> Either.right(onboardingResult)
    1 * calculationTradeCountHolder.failedProcessedTrades(CALCULATION_ID.toString()) >> 0d
    1 * cacheService.cleanCalculationCache(CALCULATION_ID.toString())
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID.toString())
    1 * calculationResultRepository.finishCalculation(CALCULATION_ID, 0d, SAVED)
  }

  def "should finish manual calculation"() {
    setup:
    def dashboardResult = new CalculationResult(
      id: CALCULATION_ID,
      calculationResultStatus: CalculationResultStatus.IN_PROGRESS,
      portfolioId: PORTFOLIO_ID,
      tradesStatistics: CalculationTradesStatistics.newOf(1l)
      )

    when:
    finalizer.onCalculationProcessed(TradesProcessedEvent.newOf(CALCULATION_ID.toHexString()))

    then:
    1 * eventPublisher.publishEvent(TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, null, null, FINISHED))
    1 * calculationResultRepository.calculationEntity(CALCULATION_ID.toString()) >> Either.right(dashboardResult)
    1 * calculationTradeCountHolder.failedProcessedTrades(CALCULATION_ID.toString()) >> 0d
    1 * cacheService.cleanCalculationCache(CALCULATION_ID.toString())
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID.toString())
    1 * calculationResultRepository.finishCalculation(CALCULATION_ID, 0d, UNSAVED)
  }

  def "should finish pnl explain calculation"() {
    setup:
    def dashboardResult = new CalculationResult(
      id: CALCULATION_ID,
      portfolioId: PORTFOLIO_ID,
      pnlExplainCalculationId: "pnlCalc1",
      calculationResultStatus: CalculationResultStatus.IN_PROGRESS,
      tradesStatistics: CalculationTradesStatistics.newOf(1l)
      )

    when:
    finalizer.onCalculationProcessed(TradesProcessedEvent.newOf(CALCULATION_ID.toHexString()))

    then:
    1 * eventPublisher.publishEvent(TradesCalculationFinishedEvent.newOf(CALCULATION_ID, PORTFOLIO_ID, null, "pnlCalc1", null, FINISHED))
    1 * calculationResultRepository.calculationEntity(CALCULATION_ID.toString()) >> Either.right(dashboardResult)
    1 * calculationTradeCountHolder.failedProcessedTrades(CALCULATION_ID.toString()) >> 0d
    1 * cacheService.cleanCalculationCache(CALCULATION_ID.toString())
    1 * calculationTradeCountHolder.removeCalculation(CALCULATION_ID.toString())
    1 * calculationResultRepository.finishCalculation(CALCULATION_ID, 0d, SAVED)
  }
}
