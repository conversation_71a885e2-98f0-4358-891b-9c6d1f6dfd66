package com.solum.xplain.calculation

import com.solum.xplain.calculation.value.CalculationPortfolioItemView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.ValuationStatus
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CalculationPortfolioItemView, includeSuperProperties = true)
class CalculationPortfolioItemViewBuilder {
  static CalculationPortfolioItemView ofMetrics() {
    new CalculationPortfolioItemViewBuilder()
      .tradeId("id")
      .tradeInfoTradeType(CoreProductType.IRS)
      .metricsPresentValue(1)
      .metricsPresentValuePayLegCurrency(1)
      .metricsCleanPresentValue(2)
      .metricsCleanPresentValueLocalCcy(2)
      .metricsPayLegPV(1)
      .metricsReceiveLegPV(1)
      .metricsDv01(2)
      .metricsDv01LocalCcy(2)
      .metricsBr01(3)
      .metricsBr01LocalCcy(3)
      .metricsInf01(4)
      .metricsInf01LocalCcy(4)
      .metricsCs01(5)
      .metricsCs01LocalCcy(5)
      .metricsInfcsbr01(6)
      .metricsInfcsbr01LocalCcy(6)
      .metricsDeltaFwd(7)
      .metricsDeltaFwdLocalCcy(7)
      .metricsDeltaSpot(8)
      .metricsDeltaSpotLocalCcy(8)
      .metricsPv01(9)
      .metricsPv01LocalCcy(9)
      .metricsClientPvPvDv01(10)
      .metricsAbsoluteDifference(11)
      .metricsRelativeDifference(11)
      .metricsAbsAbsoluteDifference(12)
      .metricsAbsRelativeDifference(12)
      .metricsClientMetricsPresentValue(13)
      .metricsPvDelta(14)
      .metricsPvDeltaLocalCcy(14)
      .metricsPvGamma(15)
      .metricsPvGammaLocalCcy(15)
      .metricsPvTheta(16)
      .metricsPvThetaLocalCcy(16)
      .metricsPvVega(17)
      .metricsPvVegaLocalCcy(17)
      .metricsBreakevenParRate(18)
      .metricsBreakevenImpliedVol(18)
      .metricsCFTZeroPay(19)
      .metricsCFTZeroRec(19)
      .metricsCFTZeroNet(20)
      .metricsCFTZeroNetReportingCcy(20)
      .metricsInflationInitialIndex(21)
      .metricsInflationFinalIndex(22)
      .valuationStatus(ValuationStatus.OK)
      .build()
  }

  static CalculationPortfolioItemView ofError() {
    new CalculationPortfolioItemViewBuilder()
      .valuationStatus(ValuationStatus.ERROR)
      .valuationError("ERR")
      .build()
  }
}
