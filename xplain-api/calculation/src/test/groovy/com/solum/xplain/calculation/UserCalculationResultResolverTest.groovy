package com.solum.xplain.calculation

import static com.solum.xplain.core.portfolio.PortfolioBuilder.portfolio
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.calculation.curves.CalculationChartGenerator
import com.solum.xplain.calculation.curves.value.CalculationResultsChartsView
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.value.CalculationResultView
import com.solum.xplain.calibration.curve.charts.ChartDateType
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.PortfolioViewBuilder
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.users.AuditUser
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import spock.lang.Specification

class UserCalculationResultResolverTest extends Specification {
  def static USER = user("id")
  def static AUTH = new TestingAuthenticationToken(USER, null)

  def userRepository = Mock(AuthenticationContext)
  def portfolioRepository = Mock(PortfolioRepository)
  def calculationResultRepository = Mock(CalculationResultRepository)
  def calculationChartGenerator = Mock(CalculationChartGenerator)

  def resolver = new UserCalculationResultResolver(
  userRepository,
  portfolioRepository,
  calculationResultRepository,
  calculationChartGenerator
  )

  def setup() {
    SecurityContextHolder.getContext().authentication = AUTH
    1 * userRepository.userEither(AUTH) >> right(USER)
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
  }

  def "should return user calculation result"() {
    setup:
    def portfolio = portfolio(AuditUser.of(USER))
    portfolio.id = "portfolioId"
    1 * portfolioRepository.getEither(portfolio.id) >> right(PortfolioViewBuilder.of(portfolio))

    def calculationResultView = new CalculationResultView(portfolioId: portfolio.id)
    1 * calculationResultRepository.calculation("calcResultId1") >> right(calculationResultView)

    when:
    def result = resolver.userCalculationResult(AUTH, "calcResultId1")

    then:
    result.isRight()
    def userCalculationResult = result.right().get() as UserCalculationResult
    userCalculationResult.view == calculationResultView
  }

  def "should return error when calculation in progress"() {
    setup:
    def calculationResultView = new CalculationResultView(calculationResultStatus: CalculationResultStatus.IN_PROGRESS)
    1 * calculationResultRepository.calculation("calcResultId1") >> right(calculationResultView)

    when:
    def result = resolver.userCalculationResult(AUTH, "calcResultId1", true)

    then:
    result.isLeft()
    def errorItem = (ErrorItem) result.left().get()
    errorItem.getDescription() == "Calculation is not finished!"
  }

  def "should not return error when calculation in progress and validate false"() {
    setup:
    def portfolio = portfolio(AuditUser.of(USER))
    portfolio.id = "portfolioId"
    1 * portfolioRepository.getEither(portfolio.id) >> right(PortfolioViewBuilder.of(portfolio))

    def calculationResultView = new CalculationResultView(calculationResultStatus: CalculationResultStatus.IN_PROGRESS, portfolioId: portfolio.id)
    1 * calculationResultRepository.calculation("calcResultId1") >> right(calculationResultView)

    when:
    def result = resolver.userCalculationResult(AUTH, "calcResultId1", false)

    then:
    result.isRight()
  }

  def "should return user calculation result with charts"() {
    setup:
    def portfolio = portfolio(AuditUser.of(USER))
    portfolio.id = "portfolioId"
    1 * portfolioRepository.getEither(portfolio.id) >> right(PortfolioViewBuilder.of(portfolio))

    def calculationResultView = new CalculationResultView(id: "ID", portfolioId: portfolio.id)
    1 * calculationResultRepository.calculation("calcResultId1") >> right(calculationResultView)

    1 * calculationChartGenerator.generateAllCharts("ID", ChartDateType.ACTUAL_DATE) >> new CalculationResultsChartsView()

    when:
    def result = resolver.userCalculationResultWithCharts(AUTH, "calcResultId1")

    then:
    result.isRight()
    def userCalculationResult = result.right().get() as UserCalculationResult
    userCalculationResult.view == calculationResultView
    userCalculationResult.view.chartData == new CalculationResultsChartsView()
  }
}
