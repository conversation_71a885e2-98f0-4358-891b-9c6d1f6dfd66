package com.solum.xplain.calculation

import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.OVERLAY
import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.calculation.form.OnboardingCalculationForm
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.company.value.PortfolioSettings
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.users.AuditUser
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import spock.lang.Specification

class OnboardingCalculationServiceTest extends Specification {
  private static def VALUATION_DATE = LocalDate.of(2021, 01, 01)
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 02))

  CalculationService calculationService = Mock()
  OnboardingCalculationService service = new OnboardingCalculationService(calculationService)

  def "should calculate portfolio"() {
    setup:
    def onboardingReportId = ObjectId.get()
    def portfolioId = "portfolioId"
    def marketDataGroupId = "marketDataGroupId"
    def marketDataGroupName = "marketDataGroupName"
    def portfolio = new PortfolioView(externalPortfolioId: portfolioId)
    def valuationSettings = new CompanyLegalEntityValuationSettingsView(
      marketDataGroupId: marketDataGroupId,
      marketDataGroupName: marketDataGroupName,
      )

    def user = AuditUser.of(user("creatorId"))
    def settings = PortfolioSettings.settings(
      portfolio,
      CompanyLegalEntitySettingsView.newOf(valuationSettings, new CompanyLegalEntityIpvSettingsView())
      )

    def form = OnboardingCalculationForm.builder()
      .valuationSettings(valuationSettings)
      .marketDataSource(OVERLAY)
      .build()

    1 * calculationService.calculateDashboardResults(
      portfolio,
      VALUATION_DATE,
      STATE_DATE,
      form,
      _) >> { List<?> args ->
        def result = (CalculationResult) args[4].apply(new CalculationResult())
        assert result.getOnboardingReportId() == onboardingReportId
        assert result.getCreatedBy() == user
        assert result.getCreatedAt() != null
        assert result.getName().startsWith("Onboarding 2021-01-01 portfolioId")
        Either.right(EntityId.entityId("id"))
      }

    when:
    def result = service.calculate(onboardingReportId, VALUATION_DATE, user, settings, OVERLAY, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull() == EntityId.entityId("id")
  }
}
