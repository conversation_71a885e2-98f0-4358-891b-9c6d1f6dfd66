package com.solum.xplain.calculation

import static com.solum.xplain.core.common.EntityId.entityId
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calculation.helpers.MockMvcConfiguration
import com.solum.xplain.calculation.value.CalculationResultForm
import com.solum.xplain.calculation.value.CalculationResultTotalsForm
import com.solum.xplain.calculation.value.CalculationResultView
import com.solum.xplain.calculation.value.CalculationResultsTotals
import com.solum.xplain.calculation.value.CashFlowValueView
import com.solum.xplain.calculation.value.CashFlowsView
import com.solum.xplain.calculation.value.DV01TradeValue
import com.solum.xplain.calculation.value.DiscountCcyCashFlows
import com.solum.xplain.calculation.value.SensitivityValue
import com.solum.xplain.calculation.value.Spot01Combined
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView
import com.solum.xplain.shared.utils.filter.TableFilter
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.mock.DetachedMockFactory

@MockMvcConfiguration
@WebMvcTest(controllers = [CalculationResultController])
class CalculationResultControllerTest extends Specification {
  def static OBJECT_ID = ObjectId.get().toHexString()
  def static EXPORTED_CSV = FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")
  private static final Sort DEFAULT_TRADE_SORTING = Sort.by(PortfolioItemFlatView.Fields.tradeInfoTradeType, PortfolioItemFlatView.Fields.portfolioId, PortfolioItemFlatView.Fields.tradeInfoExternalTradeId)

  @SpringBean
  CalculationResultControllerService service = Mock()
  @SpringBean
  CalculationResultsMetricsExportService metricsExportService = Mock()

  @Autowired
  ObjectMapper mapper

  @Autowired
  MockMvc mockMvc

  def "should get calculation totals with filter #paramName"() {
    setup:
    service.calculationTotals(_ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as GroupRequest,
      _ as CalculationResultTotalsForm) >> right(CalculationResultsTotals.empty())


    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/totals', OBJECT_ID)
      .param(paramName, "filter")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    results.getResponse().getStatus() == response
    where:
    paramName      | response
    "rowGroupCols" | 200
    "random"       | 400
  }

  def "should store portfolio calculation as permanent"() {
    setup:
    def form = new CalculationResultForm(
      name: name,
      comments: comments
      )

    service.storeAsPermanent(_ as Authentication,
      OBJECT_ID,
      _ as CalculationResultForm) >> right(entityId("id"))


    when:
    def results = mockMvc.perform(post('/calculation-result/{calculationResultId}/as-permanent', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .content(mapper.writeValueAsString(form))
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    name     | comments   | code | responseBody
    "name"   | "comments" | 200  | "id"
    "random" | null       | 200  | "id"
    "random" | ""         | 200  | "id"
    null     | "comments" | 412  | "NotEmpty.calculationResultForm.name"
    ""       | "comments" | 412  | "NotEmpty.calculationResultForm.name"
  }

  def "should get calculation results"() {
    setup:
    service.calculationResults(_ as Authentication, OBJECT_ID) >> result

    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(calculationResultView())        | 200  | "id"
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should update portfolio calculation metadata"() {
    setup:
    def form = new CalculationResultForm(
      name: name,
      comments: comments
      )

    service.updateCalculationResultsMetadata(_ as Authentication,
      OBJECT_ID,
      _ as CalculationResultForm) >> right(entityId("id"))


    when:
    def results = mockMvc.perform(put('/calculation-result/{calculationResultId}', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .content(mapper.writeValueAsString(form))
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    name     | comments   | code | responseBody
    "name"   | "comments" | 200  | "id"
    "random" | null       | 200  | "id"
    "random" | ""         | 200  | "id"
    null     | "comments" | 412  | "NotEmpty.calculationResultForm.name"
    ""       | "comments" | 412  | "NotEmpty.calculationResultForm.name"
  }

  def "should delete calculation results"() {
    setup:
    def resultId = ObjectId.get().toHexString()
    service.deleteResults(_ as Authentication, resultId) >> result

    when:
    def results = mockMvc.perform(delete('/calculation-result/{calculationResultId}', resultId)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(entityId("id"))                 | 200  | "id"
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should handle invalid result id"() {
    when:
    def results = mockMvc.perform(delete('/calculation-result/{calculationResultId}', "aaaa")
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 422
      getContentAsString().indexOf("Invalid object id") >= 0
    }
  }

  def "should get calculation cashflows"() {
    setup:
    service.calculationCashFlows(
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as CalculationResultTotalsForm) >> result
    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/cash-flows', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(cashFlowview())                 | 200  | "paymentDate"
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get calculation cashflows in csv"() {
    setup:
    metricsExportService.getCashFlowsCsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as CalculationResultTotalsForm,
      _ as TableFilter) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/cash-flows/csv', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", LocalDate.now().toString())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get DV01 trade values"() {
    setup:
    service.calculationDV01Values(
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as CalculationResultTotalsForm) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right([dv01TradeValue()])             | 200  | "curveName"
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get SPOT01 trade values"() {
    setup:
    service.calculationSpot01Values(
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as CalculationResultTotalsForm) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/spot01', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right([new Spot01Combined([])])       | 200  | "[]"
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get DV01 trade values as csv"() {
    setup:
    metricsExportService.getDV01CsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/dv01/csv', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", LocalDate.now().toString())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get BR01 trade values as csv"() {
    setup:
    metricsExportService.getBR01CsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/br01/csv', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", LocalDate.now().toString())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get INF01 trade values as csv"() {
    setup:
    metricsExportService.getINF01CsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/inf01/csv', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", LocalDate.now().toString())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get CS01 trade values as csv"() {
    setup:
    metricsExportService.getCS01CsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as List) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/cs01/csv', OBJECT_ID)
      .param("curveNames", "curveName")
      .param("stateDate", LocalDate.now().toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get Fx Delta trade values as csv"() {
    setup:
    metricsExportService.getFxDeltaCsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter,
      _ as DeltaFxType) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/fxdelta/csv', OBJECT_ID)
      .param("deltaType", DeltaFxType.DELTA_SPOT.name())
      .param("stateDate", LocalDate.now().toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get SPOT01 trade values as csv"() {
    setup:
    metricsExportService.getSpot01CsvBytes(
      _ as LocalDate,
      _ as Authentication,
      OBJECT_ID,
      _ as TableFilter) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trade-values/spot01/csv', OBJECT_ID)
      .param("stateDate", LocalDate.now().toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get calculation Items"() {
    setup:
    1 * service.getCalculationPortfolioItems(_ as Authentication,
      OBJECT_ID,
      TableFilter.emptyTableFilter(), {
        verifyAll(it, ScrollRequest) {
          sort == DEFAULT_TRADE_SORTING
        }
      }) >> result
    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/trades', OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right([ScrollableEntry.empty()])      | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get calculation Items grouped"() {
    setup:
    1 * service.getCalculationPortfolioItems(_ as Authentication,
      OBJECT_ID,
      TableFilter.emptyTableFilter(), {
        verifyAll(it, ScrollRequest) {
          sort == DEFAULT_TRADE_SORTING
        }
      },
      _ as GroupRequest) >> result
    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/trades', OBJECT_ID)
      .param("rowGroupCols", "test")
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right([ScrollableEntry.empty()])      | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get trade items in csv"() {
    setup:
    1 * service.exportCalculationResultPortfolioItems(
      _ as Authentication,
      _ as LocalDate,
      OBJECT_ID, {
        verifyAll(it, Sort) {
          it == DEFAULT_TRADE_SORTING
        }
      },
      TableFilter.emptyTableFilter()
      ) >> result
    when:
    def results = mockMvc
      .perform(get('/calculation-result/{calculationResultId}/trades-csv', OBJECT_ID)
      .param("stateDate", LocalDate.now().toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                | code | responseBody
    right(EXPORTED_CSV)                   | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get calculation logs"() {
    setup:
    service.getCalculationLogs(
      _ as Authentication,
      OBJECT_ID,
      _ as ScrollRequest,
      _ as TableFilter,
      _ as GroupRequest) >> result
    when:
    def results = mockMvc.perform(get('/calculation-result/{calculationResultId}/logs',
      OBJECT_ID)
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }


    where:
    result                                | code | responseBody
    right(ScrollableEntry.empty())        | 200  | ""
    left(Error.OBJECT_NOT_FOUND.entity()) | 422  | "OBJECT_NOT_FOUND"
  }

  static DV01TradeValue dv01TradeValue() {
    return new DV01TradeValue(
      curveName: "name",
      sensitivities: ["EUR": [new SensitivityValue()]]
      )
  }

  static CashFlowValueView cashFlowValue() {
    return new CashFlowValueView(paymentDate: LocalDate.now(),
    currency: "EUR",
    discountCcy: "USD",
    presentPay: 1.0,
    presentReceive: 1.0,
    presentValue: 1.0,
    forecastPay: 1.0,
    forecastReceive: 1.0,
    forecastValue: 1.0,
    discountFactor: 1.0,
    presentValueCalculationCcy: 1.0,
    forecastValueCalculationCcy: 1.0)
  }

  static CashFlowsView cashFlowview() {
    return CashFlowsView.of([
      new DiscountCcyCashFlows(
      paymentDate: LocalDate.now(),
      cashFlowValues: [cashFlowValue()],
      presentValueCalculationCcy: 1.0,
      forecastValueCalculationCcy: 1.0)
    ])
  }

  static CalculationResultView calculationResultView() {
    return new CalculationResultView(
      id: "id",
      calculatedAt: LocalDateTime.now(),
      valuationDate: LocalDate.now(),
      calculationType: PortfolioCalculationType.TRADES,
      calculatedBy: "test",
      currency: "EUR",
      curveConfigurationId: new ObjectId().toHexString(),
      curveConfigurationCurveGroupName: "curve",
      curveConfigurationName: "config",
      fxCurveConfigurationId: new ObjectId().toHexString(),
      fxCurveConfigurationCurveGroupName: "curve",
      fxCurveConfigurationName: "config",
      marketDataGroupId: "mdId",
      marketDataGroupName: "mdName",
      portfolioId: "ID",
      externalPortfolioId: "Name",
      savedCalculationId: "id",
      name: "name",
      comments: "comment",
      chartData: []
      )
  }


  @TestConfiguration
  static class CalculationResultControllerTestConfig {
    private DetachedMockFactory factory = new DetachedMockFactory()

    @Bean
    CalculationResultControllerService calculationResultControllerService() {
      factory.Mock(CalculationResultControllerService)
    }
  }
}
