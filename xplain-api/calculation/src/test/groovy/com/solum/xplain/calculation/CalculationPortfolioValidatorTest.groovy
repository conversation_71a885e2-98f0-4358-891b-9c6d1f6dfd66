package com.solum.xplain.calculation

import com.solum.xplain.calculation.repository.CalculationPortfolioItemRepository
import com.solum.xplain.calculation.repository.filter.AllTradesFilter
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import org.bson.types.ObjectId
import spock.lang.Specification

class CalculationPortfolioValidatorTest extends Specification {
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 01))
  private static def PORTFOLIO_ID = ObjectId.get().toHexString()

  CalculationPortfolioItemRepository itemRepository = Mock()
  CalculationPortfolioValidator validator = new CalculationPortfolioValidator(itemRepository)

  def "should return empty trades error"() {
    setup:
    def view = new PortfolioView(id: PORTFOLIO_ID)
    1 * itemRepository.hasCalculationTrades(new AllTradesFilter(PORTFOLIO_ID, STATE_DATE)) >> false
    when:
    def result = validator.validatePortfolio(view, STATE_DATE)

    then:
    result.isLeft()
    def errorItem = (ErrorItem) result.left().getOrNull()
    errorItem.description == "Portfolio has no trades"
  }

  def "should validate portfolio"() {
    setup:
    def view = new PortfolioView(id: PORTFOLIO_ID)
    1 * itemRepository.hasCalculationTrades(new AllTradesFilter(PORTFOLIO_ID, STATE_DATE)) >> true
    when:
    def result = validator.validatePortfolio(view, STATE_DATE)

    then:
    result.isRight()
  }
}
