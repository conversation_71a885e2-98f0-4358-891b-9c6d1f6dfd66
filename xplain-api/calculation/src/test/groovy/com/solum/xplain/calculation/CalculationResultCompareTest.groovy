package com.solum.xplain.calculation

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.currency.FxRate.of

import com.solum.xplain.calculation.value.CalculationPortfolioItemView
import com.solum.xplain.calculation.value.CompareItem
import com.solum.xplain.core.portfolio.CoreProductType
import spock.lang.Specification

class CalculationResultCompareTest extends Specification {

  def rateProvider = of(EUR, USD, 1.2)

  def "should calculate diff with 2 comparisons"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 1,
        metricsDv01: 2,
        metricsPv01: 3
        ))
      ], ["trade1": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 10,
        metricsDv01: 20,
        metricsPv01: 30
        ))])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValue == 1
    item.metricsPresentValueN1 == 10

    item.metricsDv01 == 2
    item.metricsDv01N1 == 20

    item.metricsPv01 == 3

    item.metricsPVN1DiffPv == 9
    item.metricsAbsPVN1DiffPv == 9
    item.metricsAbsPVN1DiffPVDivPV == 9
    item.metricsPVN1DiffPVDivPV == 9
    item.metricsPVN1DiffPVDivDV01 == 4.5
    item.metricsAbsPVN1DiffPVDivDV01 == 4.5

    item.metricsSens01 == 2
    item.metricsSens01N1 == 20
    item.metricsPVN1DiffPVDivSens01 == 4.5
    item.metricsAbsPVN1DiffPVDivSens01 == 4.5
  }


  def "should calculate diff with 2 comparisons and different currencies"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(USD, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 1.2,
        metricsDv01: 2,
        metricsPv01: 3
        ))
      ], ["trade1": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 1,
        metricsDv01: 2,
        metricsPv01: 3
        ))])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValue == item.metricsPresentValueN1
    item.metricsPresentValueN1 == 1
    item.metricsPresentValue == 1

    item.metricsDv01 == 2
    item.metricsDv01N1 == 2

    item.metricsPv01 == 3

    item.metricsPVN1DiffPv == 0
    item.metricsAbsPVN1DiffPv == 0
    item.metricsAbsPVN1DiffPVDivPV == 0
    item.metricsPVN1DiffPVDivPV == 0
    item.metricsPVN1DiffPVDivDV01 == 0
    item.metricsAbsPVN1DiffPVDivDV01 == 0

    item.metricsSens01 == 2
    item.metricsSens01N1 == 2
    item.metricsPVN1DiffPVDivSens01 == 0
    item.metricsAbsPVN1DiffPVDivSens01 == 0
  }

  def "should calculate diff with wrong 1st comparison"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 1,
        metricsDv01: 2,
        metricsPv01: 3
        ))
      ], ["random": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "random",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 100,
        metricsDv01: 200,
        metricsPv01: 300
        ))]
      )

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    !item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValue == 1
    item.metricsPresentValueN1 == null

    item.metricsDv01 == 2
    item.metricsDv01N1 == null

    item.metricsPv01 == 3

    item.metricsSens01 == 2
    item.metricsSens01N1 == null
  }

  def "should calculate diff with missing trades"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        metricsPresentValue: 1,
        metricsDv01: 2,
        metricsPv01: 3
        ))
      ], [:])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    !item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValue == 1
    item.metricsPresentValueN1 == null

    item.metricsDv01 == 2
    item.metricsDv01N1 == null

    item.metricsPv01 == 3

    item.metricsSens01 == 2
    item.metricsSens01N1 == null
  }

  def "should not compare uncomparable items"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        payLegNotionalValue: 1000
        ))
      ], ["trade1": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS,
        payLegNotionalValue: 10000
        ))])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    !item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValueN1 == null

    item.metricsDv01N1 == null
  }

  def "should calculate diff with 3 comparisons when DV01 and PV01 missing"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS
        ))
      ], ["trade1": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: CoreProductType.IRS
        ))])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    item.comparable
    item.tradeInfoTradeType == CoreProductType.IRS
    item.metricsPresentValue == null
    item.metricsPresentValueN1 == null

    item.metricsDv01 == null
    item.metricsDv01N1 == null

    item.metricsPv01 == null
  }

  def "should calculate diff using asset-specific sens01"() {
    setup:
    def compare = new CalculationResultCompare(
      EUR,
      [
        new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: productType,
        metricsPresentValue: pv,
        metricsDv01: 2,
        metricsBr01: 3,
        metricsInf01: 4,
        metricsCs01: 5,
        metricsPv01: 3
        ))
      ], ["trade1": new CompareItem(EUR, rateProvider, new CalculationPortfolioItemView(
        tradeId: "trade1",
        tradeInfoTradeType: productType,
        metricsPresentValue: pvN1,
        metricsDv01: 20,
        metricsBr01: 30,
        metricsInf01: 40,
        metricsCs01: 50,
        metricsPv01: 30
        ))])

    when:
    def results = compare.calculateResults()

    then:
    results.size() == 1
    def item = results.get(0)
    item.comparable
    item.tradeInfoTradeType == productType
    item.metricsPresentValue == pv
    item.metricsPresentValueN1 == pvN1

    item.metricsSens01 == resSens01
    item.metricsSens01N1 == resSens01N1

    item.metricsDv01 == 2
    item.metricsDv01N1 == 20

    item.metricsPVN1DiffPVDivSens01 == resPvDiffSens01
    item.metricsAbsPVN1DiffPVDivSens01 == resAbsPvDiffSens01

    where:
    productType               | pv | pvN1 | resSens01 | resSens01N1 | resPvDiffSens01 | resAbsPvDiffSens01
    CoreProductType.IRS           | 1  | 10   | 2         | 20          | 4.5             | 4.5
    CoreProductType.IRS           | 1  | -8   | 2         | 20          | -4.5            | 4.5
    CoreProductType.CAP_FLOOR     | 1  | 10   | 2         | 20          | 4.5             | 4.5
    CoreProductType.SWAPTION      | 1  | 10   | 2         | 20          | 4.5             | 4.5
    CoreProductType.XCCY          | 1  | 10   | 3         | 30          | 3.0             | 3.0
    CoreProductType.FXFWD         | 1  | 10   | 3         | 30          | 3.0             | 3.0
    CoreProductType.FXOPT         | 1  | 10   | 3         | 30          | 3.0             | 3.0
    CoreProductType.INFLATION     | 1  | 10   | 4         | 40          | 2.25            | 2.25
    CoreProductType.CDS           | 1  | 10   | 5         | 50          | 1.8             | 1.8
    CoreProductType.CREDIT_INDEX  | 1  | 10   | 5         | 50          | 1.8             | 1.8
    CoreProductType.LOAN_NOTE     | 1  | 10   | null      | null        | null            | null
  }
}
