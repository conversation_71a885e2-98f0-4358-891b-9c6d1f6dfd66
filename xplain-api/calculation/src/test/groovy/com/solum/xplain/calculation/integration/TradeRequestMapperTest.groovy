package com.solum.xplain.calculation.integration

import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.cdsTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.creditIndexTrancheTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxOptionTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxOptionTradeDetailsFlippedCurrencies
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxSingle
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.inflationSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.irsDetails

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.value.TradeCalculationRequest
import com.solum.xplain.calibration.rates.group.DiscountingGroups
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem
import com.solum.xplain.calibration.rates.group.ois.OisDiscountingGroup
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.trade.CreditTradeDetails
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.product.details.TradeDetailsResolver
import com.solum.xplain.valuation.messages.trade.ValuationCreditTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationOptionTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class TradeRequestMapperTest extends IntegrationSpecification {

  CalculationCurveGroupData result

  @Autowired
  TradeDetailsResolver tradeDetailsResolver

  @Autowired
  TradeRequestMapper mapper

  def setup() {
    result = Mock()
    result.isSensitivitiesSupported() >> true
    def discounting = Mock(DiscountingGroups)
    result.getDiscountingGroups() >> discounting
    discounting.itemDiscountingGroup(_ as DiscountableItem) >> OisDiscountingGroup.discounting(Currency.EUR)
  }

  def "should correctly map Credit request"() {
    setup:
    def details = cdsTradeDetails()
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      PortfolioItemBuilder.trade(CoreProductType.CDS, details),
      result)
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    validateCreditDetails(result.tradeDetails.creditTradeDetails, details.creditTradeDetails)
    result.tradeDetails.creditTradeDetails.creditCurveName == "SELF_EUR_SNRFOR_CR14"
    result.reportingCurrency == "EUR"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.discountingCcy == "EUR"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == "SELF_EUR_SNRFOR_CR14"
  }

  def "should correctly map FXO net ccy exposure request"() {
    setup:
    def details = fxOptionTradeDetails(LocalDate.now())

    def trade = PortfolioItemBuilder.trade(CoreProductType.FXOPT, details)
    trade.setCustomFields(customFields)

    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      trade,
      result)

    when:
    def result = mapper.valuationRequest(request)

    then:
    validateCommonFields(result.tradeDetails, details)
    result.reportingCurrency == "EUR"
    result.tradeDetails.hedgeTradeDetails.potentialHedge == expIsPotential

    where:
    customFields            | expIsPotential
    [potentialField()]      | true
    []                      | null
    [potentialField(false)] | false
    null                    | null
  }

  def "should correctly map FXFwd net ccy exposure request"() {
    setup:
    def details = fxSingle(LocalDate.now())

    def trade = PortfolioItemBuilder.trade(CoreProductType.FXFWD, details)
    trade.setCustomFields(customFields)

    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      trade,
      result)

    when:
    def result = mapper.valuationRequest(request)

    then:
    validateCommonFields(result.tradeDetails, details)
    result.reportingCurrency == "EUR"
    result.tradeDetails.hedgeTradeDetails.potentialHedge == expIsPotential

    where:
    customFields            | expIsPotential
    [potentialField()]      | true
    []                      | null
    [potentialField(false)] | false
    null                    | null
  }

  def "should correctly map Swap request"() {
    setup:
    def details = irsDetails(LocalDate.parse("2021-08-25"))
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      PortfolioItemBuilder.trade(CoreProductType.IRS, details),
      result)
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    result.reportingCurrency == "EUR"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.discountingCcy == "EUR"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == "EUR 3M"
  }

  def "should correctly map FxOption request"() {
    setup:
    def details = fxOptionTradeDetails(LocalDate.now())
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.USD,
      PortfolioItemBuilder.trade(CoreProductType.FXOPT, details),
      result
      )
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    validateOptionTradeDetails(result.tradeDetails.optionTradeDetails, details.optionTradeDetails)
    result.reportingCurrency == "USD"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.discountingCcy == "EUR"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == "EUR/USD"
  }

  def "should correctly map FxOption request where base currency is lower in priority order"() {
    setup:
    def details = fxOptionTradeDetailsFlippedCurrencies(LocalDate.now())
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.USD,
      PortfolioItemBuilder.trade(CoreProductType.FXOPT, details),
      result
      )
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    validateOptionTradeDetails(result.tradeDetails.optionTradeDetails, details.optionTradeDetails)
    result.reportingCurrency == "USD"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.discountingCcy == "EUR"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == "USD/EUR"
  }

  def "should correctly map CreditIndexTranche request"() {
    setup:
    def details = creditIndexTrancheTradeDetails(LocalDate.now())
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      PortfolioItemBuilder.trade(CoreProductType.CREDIT_INDEX_TRANCHE, details),
      result
      )
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    validateCreditDetails(result.tradeDetails.creditTradeDetails, details.creditTradeDetails)
    result.reportingCurrency == "EUR"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == "TRANCHE_CODE_3-6_EUR"
  }

  def "should correctly map Inflation request"() {
    setup:
    def details = inflationSwap(LocalDate.now(), EUR_FIXED_ZC_EU_EXT_CPI)
    details.getInfo().setCounterPartyType(counterpartyType)
    def request = TradeCalculationRequest.newOfCalculation(
      ObjectId.get().toHexString(),
      Currency.EUR,
      PortfolioItemBuilder.trade(CoreProductType.INFLATION, details),
      result
      )
    when:
    def result = mapper.valuationRequest(request)
    then:
    validateCommonFields(result.tradeDetails, details)
    result.reportingCurrency == "EUR"
    result.discountingKey == "EUR_ONSHORE_NONE"
    result.tradeDetails.info.xplainResolvedTradeUnderlying == underlying

    where:
    counterpartyType           | key                | underlying
    CounterpartyType.BILATERAL | "EUR_ONSHORE_NONE" | "EU EXT CPI"
    CounterpartyType.CLEARED   | "EUR_ONSHORE_LCH"  | "EU EXT CPI LCH"
    null                       | "EUR_ONSHORE_NONE" | "EU EXT CPI"
  }

  void validateCommonFields(ValuationTradeDetails valuationDetails, TradeDetails details) {
    verifyAll {
      details.positionType == valuationDetails.positionType
      validateLeg(valuationDetails.payLeg, details.payLeg)
      validateLeg(valuationDetails.receiveLeg, details.receiveLeg)
      details.calendar == valuationDetails.calendar
      details.businessDayConvention == valuationDetails.businessDayConvention
      details.businessDayAdjustmentType == valuationDetails.businessDayAdjustmentType
      details.notionalScheduleInitialExchange == valuationDetails.notionalScheduleInitialExchange
      details.notionalScheduleFinalExchange == valuationDetails.notionalScheduleFinalExchange
      details.stubConvention == valuationDetails.stubConvention
      details.startDate == valuationDetails.startDate
      details.endDate == valuationDetails.endDate
      details.firstRegularStartDate == valuationDetails.firstRegularStartDate
      details.lastRegularEndDate == valuationDetails.lastRegularEndDate
      details.rollConvention == valuationDetails.rollConvention
      details.fxRate == valuationDetails.fxRate
      details.info.tradeCurrency == valuationDetails.info.tradeCurrency
    }
  }

  void validateLeg(ValuationTradeLegDetails valuationLegDetails, TradeLegDetails legDetails) {
    verifyAll {
      legDetails.notional == valuationLegDetails.notional
      legDetails.currency == valuationLegDetails.currency
      legDetails.accrualFrequency == valuationLegDetails.accrualFrequency
      legDetails.accrualMethod == valuationLegDetails.accrualMethod
      legDetails.paymentFrequency == valuationLegDetails.paymentFrequency
      legDetails.paymentCompounding == valuationLegDetails.paymentCompounding
      legDetails.paymentOffsetDays == valuationLegDetails.paymentOffsetDays
      legDetails.index == valuationLegDetails.index
      legDetails.fixingDateOffsetDays == valuationLegDetails.fixingDateOffsetDays
      legDetails.dayCount == valuationLegDetails.dayCount
      legDetails.initialValue == valuationLegDetails.initialValue
      legDetails.inflationLag == valuationLegDetails.inflationLag
      legDetails.indexCalculationMethod == valuationLegDetails.indexCalculationMethod
      legDetails.overnightRateCutOffDays == valuationLegDetails.overnightRateCutOffDays
    }
  }

  void validateCreditDetails(ValuationCreditTradeDetails valuationCreditTradeDetails, CreditTradeDetails creditTradeDetails) {
    verifyAll {
      creditTradeDetails.seniority == valuationCreditTradeDetails.seniority
      creditTradeDetails.corpTicker == valuationCreditTradeDetails.corpTicker
      creditTradeDetails.creditIndexVersion == valuationCreditTradeDetails.creditIndexVersion
      creditTradeDetails.creditIndexSeries == valuationCreditTradeDetails.creditIndexSeries
      creditTradeDetails.entityLongName == valuationCreditTradeDetails.entityLongName
      creditTradeDetails.reference == valuationCreditTradeDetails.reference
      creditTradeDetails.upfront == valuationCreditTradeDetails.upfront
      creditTradeDetails.upfrontDate == valuationCreditTradeDetails.upfrontDate
      creditTradeDetails.upfrontConvention == valuationCreditTradeDetails.upfrontConvention
      creditTradeDetails.sector == valuationCreditTradeDetails.sector
      creditTradeDetails.docClause == valuationCreditTradeDetails.docClause
      creditTradeDetails.creditIndexTranche == valuationCreditTradeDetails.creditIndexTranche
    }
  }

  void validateOptionTradeDetails(ValuationOptionTradeDetails valuationCreditTradeDetails, OptionTradeDetails creditTradeDetails) {
    verifyAll {
      creditTradeDetails.capFloorType == valuationCreditTradeDetails.capFloorType
      creditTradeDetails.callPutType == valuationCreditTradeDetails.callPutType
      creditTradeDetails.expiryTime == valuationCreditTradeDetails.expiryTime
      creditTradeDetails.expiryZone == valuationCreditTradeDetails.expiryZone
      creditTradeDetails.expiryDate == valuationCreditTradeDetails.expiryDate
      creditTradeDetails.expiryDateConvention == valuationCreditTradeDetails.expiryDateConvention
      creditTradeDetails.premiumDate == valuationCreditTradeDetails.premiumDate
      creditTradeDetails.premiumDateConvention == valuationCreditTradeDetails.premiumDateConvention
      creditTradeDetails.premiumValue == valuationCreditTradeDetails.premiumValue
      creditTradeDetails.premiumCurrency == valuationCreditTradeDetails.premiumCurrency
      creditTradeDetails.swaptionSettlementType == valuationCreditTradeDetails.swaptionSettlementType
      creditTradeDetails.strike == valuationCreditTradeDetails.strike
    }
  }

  def potentialField(boolean isPotential = true) {
    return new CustomTradeField("POTENTIAL_HEDGE", isPotential.toString())
  }
}
