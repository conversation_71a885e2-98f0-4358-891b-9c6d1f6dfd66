package com.solum.xplain.calculation.optionexpiry

import static com.solum.xplain.calculation.UserCalculationResult.userCalculationResult
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_BUY
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static io.atlassian.fugue.Either.right
import static java.math.BigDecimal.ONE
import static org.springframework.data.domain.Sort.unsorted

import com.solum.xplain.calculation.UserCalculationResultResolver
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReport
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportParams
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.value.CalculationResultView
import java.time.LocalDate
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class OptionExpiryReportControllerServiceTest extends Specification {
  def static USER = user("id")
  def static AUTH = new TestingAuthenticationToken(USER, null)
  def static CALCULATION_RESULT_ID = ObjectId.get().toHexString()
  def static VALUATION_DATE = LocalDate.now()

  def userCalculationResultResolver = Mock(UserCalculationResultResolver)
  def calculationResultRepository = Mock(CalculationResultRepository)
  def optionExpiryReportGenerator = Mock(OptionExpiryReportGenerator)

  def service = new OptionExpiryReportControllerService(
  userCalculationResultResolver,
  calculationResultRepository,
  optionExpiryReportGenerator
  )

  def "should get reports"() {
    setup:
    def params = Mock(OptionExpiryReportParams)
    def calculationResultView = new CalculationResultView(id: CALCULATION_RESULT_ID, valuationDate: VALUATION_DATE)
    def userCalculationResult = userCalculationResult(USER, calculationResultView, null)
    def expectedReport = Mock(OptionExpiryReport)

    1 * userCalculationResultResolver.userCalculationResult(AUTH, CALCULATION_RESULT_ID) >> right(userCalculationResult)
    1 * calculationResultRepository.calculationPortfolioItemsForExportStream(CALCULATION_RESULT_ID, unsorted(), emptyTableFilter()) >> Stream.of()
    1 * optionExpiryReportGenerator.generateReport([], VALUATION_DATE, params) >> expectedReport

    when:
    def result = service.report(AUTH, CALCULATION_RESULT_ID, emptyTableFilter(), params)

    then:
    result.isRight()
    result.getOrNull() == expectedReport
  }

  def "should get reports csv"() {
    setup:
    def params = Mock(OptionExpiryReportParams)
    def calculationResultView = new CalculationResultView(id: CALCULATION_RESULT_ID, valuationDate: VALUATION_DATE)
    def userCalculationResult = userCalculationResult(USER, calculationResultView, null)
    def expectedReport = new OptionExpiryReport([ONE], [ONE], [(SWAPTION_PAY_BUY): new BigDecimal[][]{
        ONE
      }])

    1 * userCalculationResultResolver.userCalculationResult(AUTH, CALCULATION_RESULT_ID) >> right(userCalculationResult)
    1 * calculationResultRepository.calculationPortfolioItemsForExportStream(CALCULATION_RESULT_ID, unsorted(), emptyTableFilter()) >> Stream.of()
    1 * optionExpiryReportGenerator.generateReport([], VALUATION_DATE, params) >> expectedReport

    when:
    def result = service.reportsCsv(AUTH, CALCULATION_RESULT_ID, emptyTableFilter(), params)

    then:
    result.isRight()
    result.getOrNull().getName().startsWith(SWAPTION_PAY_BUY.name())
  }
}
