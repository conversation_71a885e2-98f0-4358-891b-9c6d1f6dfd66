package com.solum.xplain.calculation.integration

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR
import static com.solum.xplain.core.error.Error.CALCULATION_WARNING

import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.CalculationTradesStatistics
import com.solum.xplain.calculation.events.TradesProcessedEvent
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.repository.SaveOperationStatistics
import com.solum.xplain.calculation.value.InProgressValuationStatus
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.shared.datagrid.ClusterEventPublisher
import com.solum.xplain.shared.utils.ratelimit.RateLimitedOperationService
import com.solum.xplain.valuation.messages.metrics.ValuationResponse
import com.solum.xplain.valuation.messages.metrics.ValuationStatus
import io.atlassian.fugue.Either
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class CalculationResponseHandlerTest extends Specification {
  CalculationResultRepository resultRepository = Mock()
  AuditEntryService auditEntryService = Mock()
  CalculationTradeCountHolder calculationCountHolder = Mock()
  ApplicationEventPublisher eventPublisher = Mock()
  ClusterEventPublisher clusterEventPublisher = Mock()
  RateLimitedOperationService rateLimitedOperationService = Mock()
  CalculationResponseHandler listener = new CalculationResponseHandler(resultRepository, auditEntryService, calculationCountHolder, eventPublisher, clusterEventPublisher, rateLimitedOperationService
  )

  def "should correctly process error calculation responses"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      externalTradeId: "IRS",
      status: ValuationStatus.ERROR,
      errorMessage: "ERROR",
      warnings: ["WARNING"]
      )
    ]
    def auditEntry = new AuditEntry(id: "id")

    when:
    listener.handle(calculationId, items)

    then:
    1 * resultRepository.saveResults(items) >> new SaveOperationStatistics(1, 1)
    1 * auditEntryService.entryByReference("calculationResult", calculationId) >> Either.right(auditEntry)
    1 * auditEntryService.addLogs("id", [
      CALCULATION_ERROR.entity("Trade IRS. ERROR"),
      CALCULATION_WARNING.entity("Trade IRS. WARNING")
    ]) >> Either.right(auditEntry)
    1 * calculationCountHolder.allTradesProcessed(calculationId, 1) >> true
    1 * calculationCountHolder.updateFailureCount(calculationId, 1) >> 1
    1 * calculationCountHolder.remainingUnprocessedTrades(calculationId) >> 2L
    1 * eventPublisher.publishEvent(TradesProcessedEvent.newOf(calculationId))
    1 * resultRepository.calculationEntity(calculationId) >>
      Either.right(new CalculationResult(id: new ObjectId(calculationId), tradesStatistics: new CalculationTradesStatistics(totalCount: 150L)))
    1 * rateLimitedOperationService.progressOperation(calculationId, 99, false, _ as Runnable)
  }

  def "should correctly process calculation response with warnings"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      externalTradeId: "IRS",
      status: ValuationStatus.OK,
      warnings: ["WARNING"]
      )
    ]
    def auditEntry = new AuditEntry(id: "id")
    when:

    listener.handle(calculationId, items)
    then:
    1 * resultRepository.saveResults(items) >> new SaveOperationStatistics(1, 0)
    1 * auditEntryService.entryByReference("calculationResult", calculationId) >> Either.right(auditEntry)
    1 * auditEntryService.addLogs("id", [CALCULATION_WARNING.entity("Trade IRS. WARNING")]) >> Either.right(auditEntry)
    1 * calculationCountHolder.allTradesProcessed(calculationId, 1) >> true
    1 * calculationCountHolder.remainingUnprocessedTrades(calculationId) >> 2L
    0 * calculationCountHolder.updateFailureCount(calculationId, 0)
    1 * eventPublisher.publishEvent(TradesProcessedEvent.newOf(calculationId))
    1 * resultRepository.calculationEntity(calculationId) >>
      Either.right(new CalculationResult(id: new ObjectId(calculationId), tradesStatistics: new CalculationTradesStatistics(totalCount: 150L)))
    1 * rateLimitedOperationService.progressOperation(calculationId, 99, false, _ as Runnable)
  }

  def "should correctly process calculation responses without warnings"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      status: ValuationStatus.OK,
      )
    ]
    def auditEntry = new AuditEntry(id: "id")

    when:
    listener.handle(calculationId, items)

    then:
    1 * resultRepository.saveResults(items) >> new SaveOperationStatistics(1, 0)
    1 * calculationCountHolder.allTradesProcessed(calculationId, 1) >> true
    1 * calculationCountHolder.remainingUnprocessedTrades(calculationId) >> 2L
    0 * calculationCountHolder.updateFailureCount(calculationId, 0)
    1 * eventPublisher.publishEvent(TradesProcessedEvent.newOf(calculationId))
    1 * resultRepository.calculationEntity(calculationId) >>
      Either.right(new CalculationResult(id: new ObjectId(calculationId), tradesStatistics: new CalculationTradesStatistics(totalCount: 150L)))
    1 * rateLimitedOperationService.progressOperation(calculationId, 99, false, _ as Runnable)

    0 * auditEntryService.entryByReference("calculationResult", calculationId) >> Either.right(auditEntry)
    0 * auditEntryService.addLogs("id", _ as List<ErrorItem>) >> Either.right(auditEntry)
  }

  def "should not publish event when not all trades processed"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      externalTradeId: "IRS",
      status: ValuationStatus.ERROR,
      warnings: [],
      errorMessage: "ERROR"
      )
    ]
    def auditEntry = new AuditEntry(id: "id")

    when:
    listener.handle(calculationId, items)

    then:
    1 * resultRepository.saveResults(items) >> new SaveOperationStatistics(1, 1)
    1 * auditEntryService.entryByReference("calculationResult", calculationId) >> Either.right(auditEntry)
    1 * auditEntryService.addLogs("id", [CALCULATION_ERROR.entity("Trade IRS. ERROR")]) >> Either.right(auditEntry)
    1 * calculationCountHolder.allTradesProcessed(calculationId, 1) >> false
    1 * calculationCountHolder.updateFailureCount(calculationId, 1) >> 1
    1 * calculationCountHolder.remainingUnprocessedTrades(calculationId) >> 0L
    0 * eventPublisher.publishEvent(TradesProcessedEvent.newOf(calculationId))
    1 * resultRepository.calculationEntity(calculationId) >>
      Either.right(new CalculationResult(id: new ObjectId(calculationId), tradesStatistics: new CalculationTradesStatistics(totalCount: 0L)))
    1 * rateLimitedOperationService.progressOperation(calculationId, 100, true, _ as Runnable)
  }

  def "should ignore responses if calculation not present"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      status: ValuationStatus.ERROR,
      errorMessage: "ERROR"
      )
    ]

    when:
    listener.handle(calculationId, items)

    then:
    1 * resultRepository.calculationEntity(calculationId) >> Either.left(CALCULATION_ERROR.entity())
    0 * resultRepository.saveResults_
    0 * auditEntryService.entryByReference_
    0 * auditEntryService.addLogs_
    0 * calculationCountHolder.allTradesProcessed_
  }

  def "should handle duplicated response"() {
    setup:
    def calculationId = new ObjectId().toHexString()
    def items = [
      new ValuationResponse(
      calculationId: calculationId,
      status: ValuationStatus.ERROR,
      warnings: [],
      errorMessage: "ERROR"
      )
    ]

    when:
    listener.handle(calculationId, items)

    then:
    1 * resultRepository.saveResults(items) >> new SaveOperationStatistics(0, 0)
    1 * resultRepository.calculationEntity(calculationId) >>
      Either.right(new CalculationResult(id: new ObjectId(calculationId), tradesStatistics: new CalculationTradesStatistics(totalCount: 0L)))

    0 * auditEntryService.entryByReference_
    0 * auditEntryService.addLogs_
    0 * calculationCountHolder.allTradesProcessed_
    0 * calculationCountHolder.updateFailureCount_
    0 * calculationCountHolder.remainingUnprocessedTrades_
    0 * eventPublisher.publishEvent_
  }
}
