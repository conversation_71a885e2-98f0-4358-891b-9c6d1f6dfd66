package com.solum.xplain.calculation

import static java.time.LocalDate.now

import com.solum.xplain.calculation.events.TradesCalculationRequestedEvent
import com.solum.xplain.calculation.integration.CalculationRequestProducer
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.util.stream.Stream
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import spock.lang.Specification

class TradesValuationsExecutorTest extends Specification {
  CalibrationCacheService cacheService = Mock()
  CalculationResultRepository calculationResultRepository = Mock()
  CalculationRequestProducer calculationRequestProducer = Mock()
  CalculationTradeCountHolder countHolder = Mock()

  TradesValuationsExecutor executor = new TradesValuationsExecutor(cacheService, calculationResultRepository, calculationRequestProducer, countHolder)

  def "should execute trade valuation"() {
    setup:
    def stateDate = BitemporalDate.newOf(now())
    def objectId = ObjectId.get()
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> Stream.of(new TradeLegDetails(currency: "EUR"))

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def calculationTrades = Mock(CalculationTrades)
    1 * calculationTrades.tradesCount() >> 1L
    1 * calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def calculationData = Mock(PortfolioCalculationData)
    calculationData.stateDate() >> stateDate
    calculationData.getPortfolio() >> new PortfolioView(externalPortfolioId: "extId")
    calculationData.getCalculationTrades() >> calculationTrades

    def event = Mock(TradesCalculationRequestedEvent)
    event.getCalculationId() >> objectId
    event.getCalculationData() >> calculationData
    event.toResult(1l) >> new CalculationResult()
    event.toCharts() >> new CalculationResultCurves()

    when:
    executor.execute(event)

    then:
    1 * calculationResultRepository.saveCalculation(new CalculationResult(), new CalculationResultCurves()) >> new CalculationResult(id: objectId)
    1 * cacheService.cacheResults(objectId.toHexString(), calculationData)
    1 * calculationRequestProducer.sendRequest(_)
  }
}
