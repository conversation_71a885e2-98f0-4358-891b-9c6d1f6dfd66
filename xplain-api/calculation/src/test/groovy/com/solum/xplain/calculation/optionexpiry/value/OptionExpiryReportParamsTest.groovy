package com.solum.xplain.calculation.optionexpiry.value

import static java.math.BigDecimal.ONE

import spock.lang.Specification

class OptionExpiryReportParamsTest extends Specification {

  def "should return default params"() {
    setup:
    def params = new OptionExpiryReportParams(null, null, null)

    when:
    def types = params.types()
    def expiryStepSize = params.expiryStepSize()
    def itmStepCount = params.itmStepCount()

    then:
    types == OptionExpiryReportType.values().toList()
    expiryStepSize == 0.25
    itmStepCount == 20
  }

  def "should return custom params"() {
    setup:
    def params = new OptionExpiryReportParams([], ONE, 2)

    when:
    def types = params.types()
    def expiryStepSize = params.expiryStepSize()
    def itmStepCount = params.itmStepCount()

    then:
    types == []
    expiryStepSize == 1
    itmStepCount == 2
  }
}
