package com.solum.xplain.calculation.optionexpiry

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.FXOPT_CALL_BUY
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.FXOPT_CALL_SELL
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.FXOPT_PUT_BUY
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.FXOPT_PUT_SELL
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_BUY
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_SELL
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_REC_BUY
import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_REC_SELL
import static com.solum.xplain.extensions.enums.CallPutType.CALL
import static com.solum.xplain.extensions.enums.CallPutType.PUT
import static com.solum.xplain.extensions.enums.PositionType.BUY
import static com.solum.xplain.extensions.enums.PositionType.SELL
import static java.math.BigDecimal.ONE

import com.opengamma.strata.basics.currency.CurrencyPair
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportParams
import com.solum.xplain.calculation.value.CalculationPortfolioItemView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.extensions.enums.CallPutType
import com.solum.xplain.extensions.enums.PositionType
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class OptionExpiryReportGeneratorTest extends Specification {
  def static VALUATION_DATE = LocalDate.of(2022, 1, 1)

  def service = new OptionExpiryReportGenerator()

  def "should not generate SWAPTION_PAY_BUY report when no data"() {
    setup:
    def params = new OptionExpiryReportParams([SWAPTION_PAY_BUY], null, null)

    when:
    def resultNoData = service.generateReport([], VALUATION_DATE, params)

    then:
    resultNoData.values.isEmpty()

    when:
    def tWithoutDv01 = mockSwaption(BUY, "Fixed", 0.02d, VALUATION_DATE.plusMonths(5), null)
    def resultIncompleteData = service.generateReport([tWithoutDv01], VALUATION_DATE, params)

    then:
    resultIncompleteData.values.isEmpty()
  }

  def "should generate SWAPTION_PAY_BUY report"() {
    setup:
    def params = new OptionExpiryReportParams([SWAPTION_PAY_BUY], null, 13)
    def t1 = mockSwaption(BUY, "Fixed", 0.02d, VALUATION_DATE.plusMonths(5), -1.0554987)
    def t2 = mockSwaption(BUY, "Fixed", 0.02d, VALUATION_DATE.plusMonths(5), 2d)
    def t3 = mockSwaption(BUY, "Fixed", 0.05d, VALUATION_DATE.plusMonths(4), 2d)
    def t4 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(12), 2d)
    def t5 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(14), 3d)
    def t6 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(14), 3d)
    def t7 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(15), 3d)
    def tNa1 = mockSwaption(SELL, "Fixed", 0.07d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa2 = mockSwaption(BUY, "EUR-EURIBOR-3M", 0.07d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa3 = mockFxOpt(BUY, CALL, 1d)

    when:
    def result = service.generateReport([t1, t2, t3, t4, t5, t6, t7, tNa1, tNa2, tNa3], VALUATION_DATE, params)

    then:
    result.itm == [0.0068, 0.0029, -0.0010, -0.0049, -0.0088, -0.0127, -0.0166, -0.0205, -0.0244, -0.0283, -0.0322, -0.0361, -0.0400]
    result.expiry == [0.25, 0.50, 0.75, 1.0]
    result.values.size() == 1
    result.values[SWAPTION_PAY_BUY].length == result.itm.size()
    result.values[SWAPTION_PAY_BUY][0] == new BigDecimal[]{
      0.94, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][1] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][2] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][3] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][4] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][5] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][6] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][7] == new BigDecimal[]{
      2, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][8] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][9] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][10] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][11] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[SWAPTION_PAY_BUY][12] == new BigDecimal[]{
      0, 0, 0, 11
    }
  }

  def "should generate FXOPT_CALL_BUY report for fxOption trades"() {
    setup:
    def params = new OptionExpiryReportParams([FXOPT_CALL_BUY], null, 5)
    def t1 = mockFxOpt(BUY, CALL, 1d, VALUATION_DATE.plusMonths(5), 1d)
    def t2 = mockFxOpt(BUY, CALL, 1d, VALUATION_DATE.plusMonths(5), 2d)
    def t3 = mockFxOpt(BUY, CALL, 6d, VALUATION_DATE.plusMonths(4), 2d, CurrencyPair.of(USD, EUR))
    def t4 = mockFxOpt(BUY, CALL, 7d, VALUATION_DATE.plusMonths(12), 2d)
    def t5 = mockFxOpt(BUY, CALL, 7d, VALUATION_DATE.plusMonths(14), 3d)
    def t6 = mockFxOpt(BUY, CALL, 7d, VALUATION_DATE.plusMonths(14), 3d)
    def t7 = mockFxOpt(BUY, CALL, 7d, VALUATION_DATE.plusMonths(15), 3d)
    def tNa1 = mockFxOpt(SELL, CALL, 7d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa2 = mockFxOpt(BUY, PUT, 7d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa3 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(15), 3d)

    when:
    def result = service.generateReport([t1, t2, t3, t4, t5, t6, t7, tNa1, tNa2, tNa3], VALUATION_DATE, params)

    then:
    result.itm == [-0.2000, -1.4000, -2.6000, -3.8000, -5.0000]
    result.expiry == [0.25, 0.50, 0.75, 1.0]
    result.values.size() == 1
    result.values[FXOPT_CALL_BUY].length == result.itm.size()
    result.values[FXOPT_CALL_BUY][0] == new BigDecimal[]{
      3, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][1] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][2] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][3] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][4] == new BigDecimal[]{
      110, 0, 0, 11
    }
  }

  def "should generate FXOPT_CALL_BUY report for fxCollar trades"() {
    setup:
    def params = new OptionExpiryReportParams([FXOPT_CALL_BUY], null, 5)
    def t1 = mockFxCollar(BUY, CALL, 1d, VALUATION_DATE.plusMonths(5), 1d)
    def t2 = mockFxCollar(BUY, CALL, 1d, VALUATION_DATE.plusMonths(5), 2d)
    def t3 = mockFxCollar(BUY, CALL, 6d, VALUATION_DATE.plusMonths(4), 2d, CurrencyPair.of(USD, EUR))
    def t4 = mockFxCollar(BUY, CALL, 7d, VALUATION_DATE.plusMonths(12), 2d)
    def t5 = mockFxCollar(BUY, CALL, 7d, VALUATION_DATE.plusMonths(14), 3d)
    def t6 = mockFxCollar(BUY, CALL, 7d, VALUATION_DATE.plusMonths(14), 3d)
    def t7 = mockFxCollar(BUY, CALL, 7d, VALUATION_DATE.plusMonths(15), 3d)
    def tNa1 = mockFxCollar(SELL, CALL, 7d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa2 = mockFxCollar(BUY, PUT, 7d, VALUATION_DATE.plusMonths(11), 3d)
    def tNa3 = mockSwaption(BUY, "Fixed", 0.07d, VALUATION_DATE.plusMonths(15), 3d)

    when:
    def result = service.generateReport([t1, t2, t3, t4, t5, t6, t7, tNa1, tNa2, tNa3], VALUATION_DATE, params)

    then:
    result.itm == [-0.2000, -1.4000, -2.6000, -3.8000, -5.0000]
    result.expiry == [0.25, 0.50, 0.75, 1.0]
    result.values.size() == 1
    result.values[FXOPT_CALL_BUY].length == result.itm.size()
    result.values[FXOPT_CALL_BUY][0] == new BigDecimal[]{
      3, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][1] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][2] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][3] == new BigDecimal[]{
      0, 0, 0, 0
    }
    result.values[FXOPT_CALL_BUY][4] == new BigDecimal[]{
      110, 0, 0, 11
    }
  }

  @Unroll
  def "should generate swaption reports #type #expectedItm #expectedExpiry"() {
    setup:
    def params = new OptionExpiryReportParams([type], ONE, 4)
    def payBuy = mockSwaption(BUY, "Fixed", -0.01)
    def paySell = mockSwaption(SELL, "Fixed", 0d)
    def recBuy = mockSwaption(BUY, "EUR-EURIBOR-3M", 0.01)
    def recSell = mockSwaption(SELL, "EUR-EURIBOR-3M", 0.02)

    expect:
    def result = service.generateReport([payBuy, paySell, recBuy, recSell], VALUATION_DATE, params)
    result.values.size() == 1
    result.values.containsKey(type)
    result.itm == expectedItm
    result.expiry == expectedExpiry

    where:
    type              | expectedItm                          | expectedExpiry
    SWAPTION_PAY_BUY  | [0.04030, 0.04020, 0.04010, 0.04000] | [0, 1, 2]
    SWAPTION_PAY_SELL | [0.03030, 0.03020, 0.03010, 0.03000] | [0, 1, 2]
    SWAPTION_REC_BUY  | [0.98030, 0.98020, 0.98010, 0.98000] | [0, 1, 2]
    SWAPTION_REC_SELL | [0.99030, 0.99020, 0.99010, 0.99000] | [0, 1, 2]
  }

  @Unroll
  def "should generate fxopt reports #type #expectedItm #expectedExpiry"() {
    setup:
    def params = new OptionExpiryReportParams([type], ONE, 1)
    def buyCall = mockFxOpt(BUY, CALL, -1d)
    def buyPut = mockFxOpt(BUY, PUT, 0d)
    def sellCall = mockFxOpt(SELL, CALL, 1d)
    def sellPut = mockFxOpt(SELL, PUT, 2d)

    expect:
    def result = service.generateReport([buyCall, buyPut, sellCall, sellPut], VALUATION_DATE, params)
    result.values.size() == 1
    result.values.containsKey(type)
    result.itm == expectedItm
    result.expiry == expectedExpiry

    where:
    type            | expectedItm | expectedExpiry
    FXOPT_CALL_BUY  | [3]         | [0, 1, 2]
    FXOPT_PUT_BUY   | [-2]        | [0, 1, 2]
    FXOPT_CALL_SELL | [1]         | [0, 1, 2]
    FXOPT_PUT_SELL  | [0]         | [0, 1, 2]
  }

  @Unroll
  def "should generate fxcollar reports #type #expectedItm #expectedExpiry"() {
    setup:
    def params = new OptionExpiryReportParams([type], ONE, 1)
    def buyCall = mockFxCollar(BUY, CALL, -1d)
    def buyPut = mockFxCollar(BUY, PUT, 0d)
    def sellCall = mockFxCollar(SELL, CALL, 1d)
    def sellPut = mockFxCollar(SELL, PUT, 2d)

    expect:
    def result = service.generateReport([buyCall, buyPut, sellCall, sellPut], VALUATION_DATE, params)
    result.values.size() == 1
    result.values.containsKey(type)
    result.itm == expectedItm
    result.expiry == expectedExpiry

    where:
    type            | expectedItm   | expectedExpiry
    FXOPT_CALL_BUY  | [0.7]         | [0, 1, 2]
    FXOPT_PUT_BUY   | [-2]          | [0, 1, 2]
    FXOPT_CALL_SELL | [0.7]         | [0, 1, 2]
    FXOPT_PUT_SELL  | [-0.7]        | [0, 1, 2]
  }

  private mockSwaption(position, payIndex, strike, valuationDate = VALUATION_DATE.plusMonths(6), dv01 = 0.01d) {
    def swaption = Mock(CalculationPortfolioItemView)
    swaption.getTradeInfoTradeType() >> CoreProductType.SWAPTION
    swaption.getTradeInfoPosition() >> position.name()
    swaption.getPayLegIndex() >> payIndex
    swaption.getMetricsDv01() >> dv01
    swaption.getNotional() >> 10_000d
    swaption.getTradeInfoExpiryDate() >> valuationDate
    swaption.getPayLegRateMargin() >> strike
    swaption.getReceiveLegRateMargin() >> strike + 1
    swaption.getMetricsBreakevenParRate() >> 0.03d
    swaption
  }

  private mockFxOpt(PositionType position, CallPutType callPut, double strike, valuationDate = VALUATION_DATE.plusMonths(6), payLegNotionalValue = 100d, pair = CurrencyPair.of(EUR, USD)) {
    def fxOpt = Mock(CalculationPortfolioItemView)
    fxOpt.getTradeInfoTradeType() >> CoreProductType.FXOPT
    fxOpt.getTradeInfoPosition() >> position.name()
    fxOpt.getTradeInfoCallPutType() >> callPut
    fxOpt.getMetricsLocalCcy() >> EUR.getCode()
    fxOpt.getPayLegCurrency() >> pair.getBase().getCode()
    fxOpt.getReceiveLegCurrency() >> pair.getCounter().getCode()
    fxOpt.getPayLegNotionalValue() >> payLegNotionalValue
    fxOpt.getReceiveLegNotionalValue() >> -110d
    fxOpt.getTradeInfoExpiryDate() >> valuationDate
    fxOpt.getTradeInfoStrike() >> strike
    fxOpt.getMetricsBreakevenParRate() >> 2d
    fxOpt
  }

  private mockFxCollar(PositionType position, CallPutType callPut, double strike, valuationDate = VALUATION_DATE.plusMonths(6), payLegNotionalValue = 100d, pair = CurrencyPair.of(EUR, USD)) {
    def fxCollar = Mock(CalculationPortfolioItemView)
    fxCollar.getTradeInfoTradeType() >> CoreProductType.FXCOLLAR
    fxCollar.getTradeInfoPosition() >> position.name()
    fxCollar.getTradeInfoCallPutType() >> callPut
    fxCollar.getMetricsLocalCcy() >> EUR.getCode()
    fxCollar.getPayLegCurrency() >> pair.getBase().getCode()
    fxCollar.getReceiveLegCurrency() >> pair.getCounter().getCode()
    fxCollar.getPayLegNotionalValue() >> payLegNotionalValue
    fxCollar.getReceiveLegNotionalValue() >> -110d
    fxCollar.getTradeInfoExpiryDate() >> valuationDate
    fxCollar.getTradeInfoStrike() >> strike
    fxCollar.getTradeInfoOtherOptionStrike() >> 1.3d
    fxCollar.getMetricsBreakevenParRate() >> 2d
    fxCollar
  }
}
