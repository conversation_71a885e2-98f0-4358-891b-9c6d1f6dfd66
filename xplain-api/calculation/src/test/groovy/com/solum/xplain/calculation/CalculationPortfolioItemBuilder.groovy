package com.solum.xplain.calculation

import com.solum.xplain.calculation.value.CashFlowMetrics
import com.solum.xplain.calculation.value.DV01TradeValue
import com.solum.xplain.calculation.value.Metrics
import com.solum.xplain.calculation.value.MetricsBuilder
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.value.ValuationStatus
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = CalculationPortfolioItem)
class CalculationPortfolioItemBuilder {
  CalculationPortfolioItemBuilder() {}

  static def resultItem(ObjectId rId, PortfolioItem item, Metrics metrics) {
    def result = new CalculationPortfolioItem()
    result.calculationResultId = rId
    result.externalTradeId = item.externalTradeId
    result.tradeId = item.entityId
    result.productType = item.productType
    result.tradeDetails = item.tradeDetails
    result.portfolioId = item.portfolioId
    result.underlying = "underlying"
    result.notional = 0d
    result.valuationStatus = ValuationStatus.OK
    result.metrics = metrics
    result.discountingCcy = "USD"
    return result
  }

  static def resultItem(ObjectId rId, PortfolioItem item, String localCcy = null) {
    resultItem(rId, item, MetricsBuilder.metrics(localCcy))
  }

  static def resultItem(ObjectId rId,
    PortfolioItem item,
    String localCcy = null,
    List<DV01TradeValue> dv01TradeValueList,
    List<DV01TradeValue> br01TradeValueList,
    List<DV01TradeValue> inf01TradeValueList,
    List<DV01TradeValue> cs01TradeValueList,
    CashFlowMetrics cashFlowMetrics = MetricsBuilder.cashFlowMetrics()
  ) {
    def metrics = new MetricsBuilder(localCcy)
      .dv01TradeValues(dv01TradeValueList)
      .br01TradeValues(br01TradeValueList)
      .inf01TradeValues(inf01TradeValueList)
      .cs01TradeValues(cs01TradeValueList)
      .cashFlowMetrics(cashFlowMetrics)
      .build()
    resultItem(rId, item, metrics)
  }

  // dv01TradeValues

  static def withErrorResult(ObjectId rId, PortfolioItem item, ErrorItem errorItem) {
    def result = new CalculationPortfolioItem()
    result.calculationResultId = rId
    result.externalTradeId = item.externalTradeId
    result.tradeId = item.entityId
    result.productType = item.productType
    result.tradeDetails = item.tradeDetails
    result.underlying = "underlying"
    result.notional = 0d
    result.valuationStatus = ValuationStatus.ERROR
    result.valuationError = errorItem.getDescription()
    result.discountingCcy = "EUR"
    return result
  }
}
