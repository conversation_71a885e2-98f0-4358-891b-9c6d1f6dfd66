package com.solum.xplain.calculation

import static java.time.LocalDate.now

import com.solum.xplain.calculation.events.TradesCalculationRequestedEvent
import com.solum.xplain.calculation.integration.CalculationRequestProducer
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.config.AsyncConfig
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationEventPublisher
import org.springframework.test.context.ContextConfiguration
import org.springframework.web.context.request.RequestAttributes
import org.springframework.web.context.request.RequestContextHolder
import reactor.core.publisher.Flux
import spock.lang.Specification

@SpringBootTest
@ContextConfiguration(classes = [
  AsyncConfig, TradesValuationsExecutor
])
class TradeValuationsExecutorAsyncTest extends Specification {
  @SpringBean CalibrationCacheService cacheService = Mock()
  @SpringBean CalculationResultRepository calculationResultRepository = Mock()
  @SpringBean CalculationRequestProducer calculationRequestProducer = Mock()
  @SpringBean CalculationTradeCountHolder countHolder = Mock()
  @Autowired TradesValuationsExecutor executor
  @Autowired ApplicationEventPublisher eventPublisher

  def "cache service has access to request context from different thread when invoked by Spring event"() {
    given:
    def testThread = Thread.currentThread()
    def stateDate = BitemporalDate.newOf(now())
    def objectId = ObjectId.get()
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> Stream.of(new TradeLegDetails(currency: "EUR"))

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def calculationTrades = Mock(CalculationTrades)
    1 * calculationTrades.tradesCount() >> 1L
    1 * calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def calculationData = Mock(PortfolioCalculationData)
    calculationData.stateDate() >> stateDate
    calculationData.getPortfolio() >> new PortfolioView(externalPortfolioId: "extId")
    calculationData.getCalculationTrades() >> calculationTrades

    def event = Mock(TradesCalculationRequestedEvent)
    event.getCalculationId() >> objectId
    event.getCalculationData() >> calculationData
    event.toResult(1l) >> new CalculationResult()
    event.toCharts() >> new CalculationResultCurves()

    when:
    RequestContextHolder.getRequestAttributes().setAttribute("testAttribute", "testValue", RequestAttributes.SCOPE_REQUEST)
    eventPublisher.publishEvent(event)
    Thread.sleep(1000L)

    then:
    1 * countHolder.registerCalculation(objectId.toHexString(), 1)
    1 * calculationResultRepository.saveCalculation(new CalculationResult(), new CalculationResultCurves()) >> new CalculationResult(id: objectId)
    1 * cacheService.cacheResults(objectId.toHexString(), calculationData) >> { String calculationId, PortfolioCalculationData result ->
      assert Thread.currentThread() != testThread
      assert RequestContextHolder.currentRequestAttributes() != null
      assert RequestContextHolder.getRequestAttributes().getAttribute("testAttribute", RequestAttributes.SCOPE_REQUEST) == "testValue"
    }
    1 * calculationRequestProducer.sendRequest(_)
  }
}
