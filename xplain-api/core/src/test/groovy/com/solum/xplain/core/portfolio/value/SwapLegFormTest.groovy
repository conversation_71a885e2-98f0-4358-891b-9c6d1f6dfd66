package com.solum.xplain.core.portfolio.value

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.core.portfolio.value.SwapLegFormBuilder.swapLeg

import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.product.common.PayReceive
import com.opengamma.strata.product.swap.OvernightAccrualMethod
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg
import com.solum.xplain.core.portfolio.builder.ResolvableInflationLeg
import com.solum.xplain.core.portfolio.builder.ResolvableOvernightLeg
import com.solum.xplain.core.portfolio.builder.ResolvableTermOvernightLeg
import com.solum.xplain.core.portfolio.form.SwapLegForm
import spock.lang.Specification

class SwapLegFormTest extends Specification {

  def "should convert to swap leg"() {
    setup:
    def form = swapLeg()

    when:
    def result = form.resolvableDetails()
    then:
    result.isRight()
    def leg = result.getOrNull().toTradeLegDetails()
    leg.extLegIdentifier == "SWAP_LEG_ID"
    leg.payReceive == PayReceive.PAY
    leg.notional == 1
    leg.type == CalculationType.FIXED
    leg.accrualFrequency == "1M"
    leg.paymentOffsetDays == -1
    leg.paymentFrequency == "1M"
    leg.paymentCompounding == "None"
    leg.currency == "EUR"
    leg.dayCount == DayCounts.ACT_360.name
    leg.initialValue == 1
  }

  def "should parse ibor rate calculation"() {
    setup:
    def form = new SwapLegForm(
      extLegIdentifier: "PAY_ID",
      payReceive: "PAY",
      paymentOffsetDays: 0,
      accrualFrequency: "1M",
      paymentFrequency: "1M",
      compoundingMethod: "None",
      notionalCurrency: "EUR",
      notionalValue: 1,
      calculationType: CalculationType.IBOR,
      calculationIborDayCount: "Act/360",
      calculationIborIndex: "EUR-EURIBOR-3M",
      calculationIborFixingDateOffsetDays: -1,
      calculationIborSpreadInitialValue: 0
      )

    when:
    def leg = form.resolvableDetails().getOrNull().toTradeLegDetails()

    then:
    leg.extLegIdentifier == "PAY_ID"
    leg.dayCount == DayCounts.ACT_360.name
    leg.index == IborIndices.EUR_EURIBOR_3M.name
    leg.fixingDateOffsetDays == -1
    leg.initialValue == 0
    leg.paymentOffsetDays == 0
  }

  def "should parse inflation index calculation"() {
    setup:
    def form = new SwapLegForm(
      extLegIdentifier: "PAY_ID",
      payReceive: "PAY",
      accrualFrequency: "1M",
      paymentFrequency: "1M",
      compoundingMethod: "None",
      notionalCurrency: "EUR",
      notionalValue: 1,
      calculationType: CalculationType.INFLATION,
      calculationInflationIndex: "EU-EXT-CPI",
      calculationInflationLag: "1W",
      paymentOffsetDays: 0,
      indexCalculationMethod: "Monthly"
      )

    when:
    def leg = form.resolvableDetails().getOrNull().toTradeLegDetails()

    then:
    leg.extLegIdentifier == "PAY_ID"
    leg.index == PriceIndices.EU_EXT_CPI.name
    leg.inflationLag == "1W"
  }

  def "should parse overnight index calculation"() {
    setup:
    def form = new SwapLegForm(
      extLegIdentifier: "PAY_ID",
      calculationType: CalculationType.OVERNIGHT,
      payReceive: "PAY",
      accrualFrequency: "1M",
      paymentFrequency: "1M",
      compoundingMethod: "None",
      notionalCurrency: "EUR",
      notionalValue: 1,
      calculationOvernightDayCount: "Act/360",
      calculationOvernightIndex: "EUR-EONIA",
      calculationOvernightSpreadInitialValue: 2,
      calculationOvernightRateCutOffDays: 0,
      calculationOvernightAccrualMethod: OvernightAccrualMethod.COMPOUNDED.toString(),
      paymentOffsetDays: 0,
      )
    when:
    def parsedLeg = form.resolvableDetails().getOrNull().toTradeLegDetails()

    then:
    parsedLeg.extLegIdentifier == "PAY_ID"
    parsedLeg.index == OvernightIndices.EUR_EONIA.getName()
    parsedLeg.dayCount == DayCounts.ACT_360.getName()
    parsedLeg.overnightRateCutOffDays == 0
    parsedLeg.accrualMethod == OvernightAccrualMethod.COMPOUNDED.toString()
    parsedLeg.initialValue == 2
  }

  def "should parse term overnight index calculation"() {
    setup:
    def form = new SwapLegForm(
      extLegIdentifier: "PAY_ID",
      calculationType: CalculationType.TERM_OVERNIGHT,
      payReceive: "PAY",
      accrualFrequency: "1M",
      paymentFrequency: "1M",
      compoundingMethod: "NONE",
      notionalCurrency: "EUR",
      notionalValue: 1,
      calculationTermOvernightDayCount: "Act/360",
      calculationTermOvernightIndex: "EUR-STR",
      calculationTermOvernightAccrualMethod: "Compounded",
      calculationTermOvernightSpreadInitialValue: 0.25,
      calculationTermOvernightFixingDateOffsetDays: -2,
      calculationTermOvernightRateCutOffDays: 0,
      paymentOffsetDays: 0,
      )

    when:
    def parsedLeg = form.resolvableDetails().getOrNull().toTradeLegDetails()

    then:
    parsedLeg.extLegIdentifier == "PAY_ID"
    parsedLeg.type == CalculationType.TERM_OVERNIGHT
    parsedLeg.index == "EUR-STR"
    parsedLeg.dayCount == DayCounts.ACT_360.getName()
    parsedLeg.accrualMethod == "Compounded"
    parsedLeg.initialValue == 0.25
    parsedLeg.fixingDateOffsetDays == -2
  }

  def "should return error if no calculation"() {
    setup:
    def form = new SwapLegForm()

    when:
    def result = form.resolvableDetails()

    then:
    result.isLeft()
    result.left().get().reason == Error.UNEXPECTED_TYPE
  }

  def "should convert Fixed Leg"() {
    setup:
    def leg = ResolvableFixedLeg.builder()
      .extLegIdentifier("PAY_ID")
      .initialValue(1)
      .payReceive(PayReceive.PAY)
      .currency(EUR)
      .notional(10_000_000d)
      .accrualFrequency(Frequency.P12M)
      .paymentFrequency(Frequency.P3M)
      .paymentOffsetDays(1)
      .paymentCompounding("NONE")
      .dayCount(DayCounts.ACT_360)
      .build()
      .toTradeLegDetails()
    when:
    def form = SwapLegForm.fromSwapLeg(leg)

    then:
    form.extLegIdentifier == "PAY_ID"
    form.calculationType == CalculationType.FIXED
    form.calculationFixedDayCount == "Act/360"
    form.calculationFixedRateInitialValue == 1
    form.accrualFrequency == "12M"
    form.paymentFrequency == "3M"
    form.compoundingMethod == "NONE"
    form.notionalValue == 10_000_000d
    form.notionalCurrency == "EUR"
    form.payReceive == "Pay"
  }

  def "should convert Inflation Leg"() {
    setup:
    def leg = ResolvableInflationLeg.builder()
      .extLegIdentifier("PAY_ID")
      .payReceive(PayReceive.PAY)
      .currency(EUR)
      .notional(10_000_000d)
      .accrualFrequency(Frequency.P12M)
      .paymentFrequency(Frequency.P3M)
      .paymentOffsetDays(1)
      .paymentCompounding("NONE")
      .index("EU-EXT-CPI")
      .inflationLag("1D")
      .indexCalculationMethod(PriceIndexCalculationMethod.INTERPOLATED.toString())
      .build()
      .toTradeLegDetails()
    when:
    def form = SwapLegForm.fromSwapLeg(leg)

    then:
    form.extLegIdentifier == "PAY_ID"
    form.calculationType == CalculationType.INFLATION
    form.calculationInflationIndex == "EU-EXT-CPI"
    form.calculationInflationLag == "1D"
    form.accrualFrequency == "12M"
    form.paymentFrequency == "3M"
    form.compoundingMethod == "NONE"
    form.notionalValue == 10_000_000d
    form.notionalCurrency == "EUR"
    form.payReceive == "Pay"
    form.indexCalculationMethod == "Interpolated"
  }

  def "should convert Ibor Leg"() {
    setup:
    def leg = ResolvableIborLeg.builder()
      .extLegIdentifier("PAY_ID")
      .payReceive(PayReceive.PAY)
      .currency(EUR)
      .notional(10_000_000d)
      .accrualFrequency(Frequency.P12M)
      .paymentFrequency(Frequency.P3M)
      .paymentOffsetDays(1)
      .paymentCompounding("NONE")
      .index("EUR-EURIBOR-3M")
      .dayCount(DayCounts.ACT_360)
      .initialValue(1d)
      .fixingDateOffset(2)
      .build()
      .toTradeLegDetails()
    when:
    def form = SwapLegForm.fromSwapLeg(leg)

    then:
    form.extLegIdentifier == "PAY_ID"
    form.calculationType == CalculationType.IBOR
    form.calculationIborIndex == "EUR-EURIBOR-3M"
    form.calculationIborDayCount == "Act/360"
    form.calculationIborSpreadInitialValue == 1
    form.calculationIborFixingDateOffsetDays == 2
    form.accrualFrequency == "12M"
    form.paymentFrequency == "3M"
    form.compoundingMethod == "NONE"
    form.notionalValue == 10_000_000d
    form.notionalCurrency == "EUR"
    form.payReceive == "Pay"
  }

  def "should convert Overnight leg"() {
    setup:
    def leg = ResolvableOvernightLeg.builder()
      .extLegIdentifier("PAY_ID")
      .payReceive(PayReceive.PAY)
      .currency(EUR)
      .notional(10_000_000d)
      .accrualFrequency(Frequency.P12M)
      .paymentFrequency(Frequency.P3M)
      .paymentOffsetDays(1)
      .paymentCompounding("NONE")
      .index("EUR-EONIA")
      .dayCount(DayCounts.ACT_360)
      .initialValue(1d)
      .overnightAccrualMethod(OvernightAccrualMethod.COMPOUNDED)
      .rateCutOffDays(0)
      .build()
      .toTradeLegDetails()
    when:
    def form = SwapLegForm.fromSwapLeg(leg)

    then:
    form.extLegIdentifier == "PAY_ID"
    form.calculationType == CalculationType.OVERNIGHT
    form.calculationOvernightIndex == "EUR-EONIA"
    form.calculationOvernightDayCount == "Act/360"
    form.calculationOvernightSpreadInitialValue == 1
    form.accrualFrequency == "12M"
    form.paymentFrequency == "3M"
    form.compoundingMethod == "NONE"
    form.notionalValue == 10_000_000d
    form.notionalCurrency == "EUR"
    form.payReceive == "Pay"
  }

  def "should convert Term Overnight Leg"() {
    setup:
    def leg = ResolvableTermOvernightLeg.builder()
      .extLegIdentifier("PAY_ID")
      .payReceive(PayReceive.PAY)
      .currency(EUR)
      .notional(10_000_000d)
      .overnightAccrualMethod(OvernightAccrualMethod.COMPOUNDED)
      .accrualFrequency(Frequency.P12M)
      .paymentFrequency(Frequency.P3M)
      .paymentOffsetDays(1)
      .paymentCompounding("NONE")
      .rateCutOffDays(0)
      .index("EUR-STR")
      .dayCount(DayCounts.ACT_360)
      .initialValue(0.15d)
      .fixingDateOffset(-2)
      .build()
      .toTradeLegDetails()

    when:
    def form = SwapLegForm.fromSwapLeg(leg)

    then:
    form.extLegIdentifier == "PAY_ID"
    form.calculationType == CalculationType.TERM_OVERNIGHT
    form.calculationTermOvernightIndex == "EUR-STR"
    form.calculationTermOvernightDayCount == "Act/360"
    form.calculationTermOvernightAccrualMethod == "Compounded"
    form.calculationTermOvernightSpreadInitialValue == 0.15
    form.calculationTermOvernightFixingDateOffsetDays == -2
    form.accrualFrequency == "12M"
    form.paymentFrequency == "3M"
    form.compoundingMethod == "NONE"
    form.notionalValue == 10_000_000d
    form.notionalCurrency == "EUR"
    form.payReceive == "Pay"
  }
}
