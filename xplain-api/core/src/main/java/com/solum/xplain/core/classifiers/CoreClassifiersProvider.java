package com.solum.xplain.core.classifiers;

import static com.google.common.collect.ImmutableSet.copyOf;
import static com.opengamma.strata.basics.date.Tenor.TENOR_18M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_1M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_1Y;
import static com.opengamma.strata.basics.date.Tenor.TENOR_2Y;
import static com.opengamma.strata.basics.date.Tenor.TENOR_3M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_3Y;
import static com.opengamma.strata.basics.date.Tenor.TENOR_6M;
import static com.opengamma.strata.basics.date.Tenor.TENOR_9M;
import static com.solum.xplain.core.classifiers.BondCurveNodeTypes.BOND_YIELD_NODE;
import static com.solum.xplain.core.classifiers.ClassifierUtils.enumClassifier;
import static com.solum.xplain.core.classifiers.ClassifierUtils.sortById;
import static com.solum.xplain.core.classifiers.ClassifierUtils.sortByName;
import static com.solum.xplain.core.classifiers.ClassifierUtils.valuesStream;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CAPLET_VALUATION_MODEL_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CORE_ASSET_CLASSES;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_FREQUENCY_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_INDEX_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_INDEX_TRANCHE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_QUOTE_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_SENIORITY_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURRENCY_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURRENCY_PAIR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_NODE_CLASH_ACTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DOC_CLAUSE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.FIXED_ACCRUAL_METHOD_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.INFLATION_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.IR_INSTRUMENTS_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.MIN_NODE_GAP_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.ROLL_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.STRIPPING_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.STUB_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SUPPORTED_CALENDARS_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SURFACE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SWAPTION_SETTLEMENT_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.VOLATILITY_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.Constants.ALLOWED_DISCOUNTING_CURRENCIES;
import static com.solum.xplain.core.classifiers.Constants.CREDIT_CURVE_FIXED_RATE_BPS;
import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;
import static com.solum.xplain.core.classifiers.Constants.EXPOSURE_SHIFT_TYPES;
import static com.solum.xplain.core.classifiers.Constants.FIRST_TIME_ZONES;
import static com.solum.xplain.core.classifiers.Constants.FRA_SETTLEMENT_VALUES;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_AUD;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_CAD;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_CHF;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_EUR;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_GBP;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_JPY;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_NZD;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES_DISCOUNT_USD;
import static com.solum.xplain.core.classifiers.Constants.SERIAL_FUTURE_VALUES;
import static com.solum.xplain.core.classifiers.Constants.SWAP_LEG_FREQUENCIES;
import static com.solum.xplain.core.classifiers.Constants.XCCY_FALLBACK_CURRENCIES;
import static com.solum.xplain.core.classifiers.CreditTrancheIndices.permissibleIndicesForTranche;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.type.SupportedBusinessDayConvention.supportedBusinessDayConventions;
import static com.solum.xplain.core.classifiers.type.SupportedDayCount.supportedDayCounts;
import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.NONE;
import static com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfigurations.VOLATILITY_SURFACES;
import static com.solum.xplain.core.curvegroup.conventions.index.InflationCurveConventions.findByIndexAndClearingHouse;
import static com.solum.xplain.core.utils.FrequencyUtils.toStringNoPrefix;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.CREDIT_CURRENCIES_ORDER;
import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_PAIRS;
import static com.solum.xplain.extensions.enums.BusinessDayAdjustmentType.PAYMENT_ONLY;
import static java.util.Comparator.comparing;
import static java.util.Comparator.comparingInt;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.FxIndex;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.collect.named.NamedEnum;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators;
import com.opengamma.strata.product.credit.type.CdsConvention;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import com.solum.xplain.core.classifiers.conventions.ConventionMapper;
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies;
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.classifiers.type.SupportedDayCount;
import com.solum.xplain.core.classifiers.xm.CoreThresholdLevel;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfiguration;
import com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions;
import com.solum.xplain.core.curvegroup.conventions.fx.XccyCurveConventions;
import com.solum.xplain.core.curvegroup.conventions.index.IndexBasisCurveConventions;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.conventions.index.InflationCurveConventions;
import com.solum.xplain.core.curvegroup.conventions.index.IrIndexCurveConventions;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel;
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.fixings.FixingIndices;
import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.market.value.MdkProviderBidAskType;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.CoreProductTypeGroup;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.portfolio.value.CounterpartyType;
import com.solum.xplain.core.portfolio.value.FxLongShort;
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType;
import com.solum.xplain.core.portfolio.value.ValuationStatus;
import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.settings.value.CurveStrippingProductPriority;
import com.solum.xplain.core.settings.value.StrippingCurvePriority;
import com.solum.xplain.extensions.calendar.NthWeekdayOfMonthRollConvention;
import com.solum.xplain.extensions.curve.ExtendedCurveInterpolators;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.enums.CallPutType;
import com.solum.xplain.extensions.enums.CapFloorType;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.CreditSeniority;
import com.solum.xplain.extensions.enums.PositionType;
import com.solum.xplain.extensions.override.OverridableFixedOvernightSwapConventions;
import com.solum.xplain.extensions.override.OverridableOvernightIndexes;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;

@Component
public class CoreClassifiersProvider implements ClassifiersProvider {

  private static final List<Classifier> AVAILABLE_TIME_ZONES = availableTimeZones();
  public static final String ALL_INSTRUMENTS = "allInstruments";
  public static final String INSTRUMENT_TYPES = "ungroupedInstrumentTypes";
  public static final String ALL_ASSET_CLASS_GROUPS = "allAssetClassGroups";
  public static final String ALL_ASSET_CLASSES = "allAssetClasses";
  public static final String ASSET_CLASSES = "ungroupedAssetClasses";
  public static final String FIXING_INDICES = "fixingProjectionIndices";
  public static final String BOND_CURVE_NAMES_BY_CCY = "bondCurveNamesGroupedByCcy";
  public static final String LOAN_NOTE_DAY_COUNTS = "loanDayCounts";
  public static final String JP_LOAN_NOTE_DAY_COUNTS = "jpLoanDayCounts";
  public static final String MDK_BIDASK_TYPES = "mdkProviderBidAskTypes";
  public static final String MD_VALUE_BIDASK_TYPES = "mdValueBidAskTypes";
  public static final String INSTRUMENT_PRICE_TYPES = "instrumentPriceTypes";
  public static final String PRICING_SLOTS = "pricingSlots";
  public static final String PRODUCT_TYPE_GROUP = "productTypeGroup";
  public static final String PRODUCT_TYPE = "productType";
  public static final String SLA_DEADLINES = "slaDeadlines";
  public static final String BREAK_TEST_THRESHOLD_LEVEL = "breakTestThresholdLevel";

  public static final CoreClassifiersProvider VALIDATION_INSTANCE = new CoreClassifiersProvider();

  @Override
  public List<Classifier> classifiers() {
    return List.of(
        productType(),
        productTypeGroup(),
        coreProductType(),
        coreProductTypeGroup(),
        position(),
        fxLongShort(),
        callPutType(),
        capFloorType(),
        rollConvention(),
        currency(),
        fxTradeCurrency(),
        dayCount(),
        iborIndex(),
        iborIndexDiscountEur(),
        iborIndexDiscountUsd(),
        iborIndexDiscountGbp(),
        iborIndexDiscountAud(),
        iborIndexDiscountCad(),
        iborIndexDiscountChf(),
        iborIndexDiscountJpy(),
        iborIndexDiscountNzd(),
        priceIndex(),
        overnightIndex(),
        fxIndex(),
        discountFactorCurveInterpolators(),
        defaultCurveInterpolators(),
        curveExtrapolator(),
        dfInterpolatorExtrapolators(),
        surfaceExtrapolator(),
        curveNodeType(),
        timeZones(),
        scheduleConventions(),
        priceIndexCalculationMethod(),
        curveTypes(),
        compoundingMethods(),
        overnightAccrualMethod(),
        fixedAccrualMethod(),
        stubConvention(),
        curveNodeClashAction(),
        cdsQuoteConvention(),
        simulationsCount(),
        portfolioCalculationType(),
        swapLegFrequency(),
        fraSettlement(),
        serialFuture(),
        allMdkInstruments(),
        instrumentTypes(),
        mdkRateInstruments(),
        mdkCreditInstruments(),
        mdkFxRateInstruments(),
        curveStrippingProductPriorities(),
        curveStrippingCurvePriorities(),
        creditCurveSeniority(),
        irCurveNames(),
        tenorBasisCurveNames(),
        xccyCurveNames(),
        fxSwapCurrencies(),
        inflationCurveNames(),
        inflationCurveIndices(),
        bondCurveNames(),
        bondCurveNodeConventions(),
        indexNodeConventions(),
        xccyNodeConventions(),
        tenorBasisNodeConventions(),
        inflationNodeConventions(),
        inflationIndexConventions(),
        allAssetClasses(),
        coreAssetClasses(),
        assetClasses(),
        irInstruments(),
        cdsConvention(),
        creditCurveFixedRate(),
        assetClassGroups(),
        valuationSettingsTypes(),
        curveConfigurationType(),
        xvaCalculationDiscountingType(),
        calculationDiscountingType(),
        volatilitySurfaceNames(),
        fixingProjectionIndices(),
        marketDataSourceType(),
        discountingCurrencies(),
        xccyFallbackCurrencies(),
        indexedDiscountingCurrencies(),
        currencyPair(),
        marketDataProviderType(),
        creditDocClauses(),
        creditSectors(),
        convexityAdjTenors(),
        convexityAdjCurveNames(),
        iborIndexByCcy(),
        capFloorTradeIborIndexByCcy(),
        swaptionTradeIborIndexByCcy(),
        overnightIndexByCcy(),
        inflationIndexByCcy(),
        counterpartyType(),
        valuationStatus(),
        cdsIndex(),
        creditTrancheIndices(),
        allPermissibleTranches(),
        creditType(),
        volatilitySwapConventions(),
        strippingType(),
        creditFrequency(),
        tradeCalculationType(),
        minNodeGaps(),
        capletValuationModel(),
        volatilitySurfaceType(),
        swaptionSettlementType(),
        defaultCurveValueType(),
        inflationCurveValueType(),
        businessDayAdjustmentType(),
        inflationBusinessDayAdjustmentType(),
        supportedCalendars(),
        loanNoteDayCounts(),
        jpLoanNoteDayCounts(),
        mdkProviderBidAskTypes(),
        mdValueBidAskTypes(),
        instrumentPriceTypes(),
        pricingSlot(),
        breakTestThresholdLevel(),
        slaDeadline(),
        exposureShiftType(),
        versionState(),
        overridableOvernightIndexConventionsClassifier(),
        overnightTermIndexByCcy(),
        overridableFixedOvernightSwapConventionsClassifier());
  }

  private Classifier versionState() {
    return enumClassifier("versionState", State.class, State::name);
  }

  private Classifier coreAssetClasses() {
    return new Classifier(CORE_ASSET_CLASSES, null, groupedAssetClasses(), CoreAssetGroup.class);
  }

  private Classifier allAssetClasses() {
    return new Classifier(ALL_ASSET_CLASSES, null, groupedAssetClasses(), AssetGroup.class);
  }

  private static List<Classifier> groupedAssetClasses() {
    return Stream.of(CoreAssetGroup.values()).map(CoreClassifiersProvider::assetClasses).toList();
  }

  @Override
  public int sortOrder() {
    return 1;
  }

  private static String toBpsLabel(Double number) {
    return String.format("%.0f", number * 10000);
  }

  private static Classifier volatilitySurfacesSwapConventions(
      VolatilitySurfaceConfiguration volatilitySurface) {
    return new Classifier(
        volatilitySurface.getName(),
        List.of(new Classifier(volatilitySurface.getSwapConvention())));
  }

  public static Classifier fixingProjectionIndices() {
    return new Classifier(
        FIXING_INDICES,
        FixingIndices.FIXING_INDICES.stream()
            .map(Index::getName)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier allMdkInstruments() {
    var coreInstrumentTypes =
        Arrays.stream(CoreAssetGroup.values())
            .map(
                group -> {
                  var values =
                      Arrays.stream(CoreInstrumentType.values())
                          .filter(v -> v.assetClassGroup() == group)
                          .map(i -> new Classifier(i.name(), i.getLabel()))
                          .sorted(comparing((Classifier::getId)))
                          .toList();
                  return new Classifier(group.name(), values);
                })
            .toList();
    return new Classifier(ALL_INSTRUMENTS, coreInstrumentTypes);
  }

  private Classifier instrumentTypes() {
    return new Classifier(
        INSTRUMENT_TYPES,
        null,
        Arrays.stream(CoreInstrumentType.values())
            .map(c -> new Classifier(c.name(), c.getLabel()))
            .toList(),
        InstrumentType.class);
  }

  private Classifier assetClassGroups() {
    return new Classifier(
        ALL_ASSET_CLASS_GROUPS,
        null,
        Arrays.stream(CoreAssetGroup.values()).map(Enum::name).map(Classifier::new).toList(),
        AssetGroup.class);
  }

  public static Classifier volatilitySurfaceNames() {
    return new Classifier(
        "volatilitySurfaceNames",
        VOLATILITY_SURFACES.stream()
            .map(CoreClassifiersProvider::volatilitySurfacesSwapConventions)
            .toList());
  }

  public static Classifier marketDataSourceType() {
    return enumClassifier(
        "marketDataSourceType",
        MarketDataSourceType.class,
        MarketDataSourceType::name,
        MarketDataSourceType::getName);
  }

  public static Classifier cdsQuoteConvention() {
    return sortById(
        enumClassifier(
            CREDIT_QUOTE_CONVENTION_CLASSIFIER,
            CdsQuoteConvention.class,
            CdsQuoteConvention::name));
  }

  public static Classifier swapConvention(Stream<Classifier> values) {
    return new Classifier(
        "swapConvention",
        values
            .sorted((c1, c2) -> SwapConventionComparator.INSTANCE.compare(c1.getId(), c2.getId()))
            .toList());
  }

  public static Classifier dayCount() {
    return new Classifier(
        "dayCount",
        supportedDayCounts().stream()
            .map(c -> new Classifier(c.dayCountName(), c.label()))
            .toList());
  }

  public static Classifier cdsConvention() {
    return new Classifier(
        "cdsCurveConvention",
        CdsConvention.extendedEnum().lookupAllNormalized().values().stream()
            .collect(groupingBy(CdsConvention::getCurrency))
            .entrySet()
            .stream()
            .sorted(orderCdsConventions())
            .map(v -> cdsCurveCalendar(v.getKey(), v.getValue()))
            .toList());
  }

  private static <T extends FloatingRateIndex> List<Classifier> indiciesByCcy(List<T> indices) {
    return indices.stream().collect(groupingBy(FloatingRateIndex::getCurrency)).entrySet().stream()
        .map(
            m ->
                new Classifier(
                    m.getKey().getCode(),
                    m.getValue().stream()
                        .map(i -> new Classifier(i.getName()))
                        .sorted(comparing(Classifier::getId))
                        .toList()))
        .sorted(comparing(Classifier::getId))
        .toList();
  }

  private static Classifier cdsCurveConvention(CdsConvention cdsConvention) {
    return new Classifier(
        cdsConvention.getSettlementDateOffset().getCalendar().getName(),
        List.of(new Classifier(cdsConvention.getName())));
  }

  private static Classifier curveConventionClassifier(ConventionalCurveConvention curveConvention) {
    return new Classifier(curveConvention.getName(), nodeTypeClassifier(curveConvention));
  }

  private static List<Classifier> nodeTypeClassifier(ConventionalCurveConvention convention) {
    return convention.getNodeConventions().stream()
        .collect(Collectors.groupingBy(CurveNodeTypes::nodeTypeFromConvention))
        .entrySet()
        .stream()
        .filter(v -> v.getKey().isPresent())
        .map(
            v ->
                new Classifier(
                    v.getKey().get(),
                    v.getValue().stream().map(Named::getName).map(Classifier::new).toList()))
        .toList();
  }

  private static Classifier assetClasses(CoreAssetGroup group) {
    return new Classifier(
        group.name(),
        group.getLabel(),
        group.getAssetClasses().stream().map(c -> new Classifier(c.name(), c.getLabel())).toList());
  }

  private Classifier assetClasses() {
    return new Classifier(
        ASSET_CLASSES,
        null,
        Stream.of(CoreAssetClass.values())
            .map(v -> new Classifier(v.name(), v.getLabel()))
            .toList(),
        AssetClass.class);
  }

  private static Classifier creditCurveFixedRate() {
    return new Classifier(
        "creditFixedRate",
        CREDIT_CURVE_FIXED_RATE_BPS.stream()
            .map(r -> new Classifier(r.toString(), toBpsLabel(r)))
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private static Comparator<Entry<Currency, List<CdsConvention>>> orderCdsConventions() {
    return Comparator.comparingInt(c -> CREDIT_CURRENCIES_ORDER.indexOf(c.getKey()));
  }

  private static Classifier cdsCurveCalendar(
      Currency currency, List<CdsConvention> cdsConventions) {
    return new Classifier(
        currency.getCode(),
        cdsConventions.stream().map(CoreClassifiersProvider::cdsCurveConvention).toList());
  }

  private static Classifier fxTradeCurrency() {
    return new Classifier(
        "fxTradeCurrency",
        EXPLICIT_CURRENCIES.stream()
            .sorted(CurrencyComparator.INSTANCE)
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  public static Classifier curveStrippingCurvePriorities() {
    return sortById(
        enumClassifier(
            "curveStrippingCurvePriorities",
            StrippingCurvePriority.class,
            StrippingCurvePriority::name,
            StrippingCurvePriority::getLabel));
  }

  public static Classifier irCurveNames() {
    return new Classifier(
        "irCurveNames",
        IrIndexCurveConventions.INDEX_CURVES.stream()
            .map(CurveConvention::getName)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  public static Classifier tenorBasisCurveNames() {
    return new Classifier(
        "tenorBasisCurveNames",
        IndexBasisCurveConventions.INDEX_BASIS_CURVES.stream()
            .map(CurveConvention::getName)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  public static Classifier xccyCurveNames() {
    return new Classifier(
        "xccyCurveNames",
        XccyCurveConventions.XCCY_CURVES.stream()
            .map(CurveConvention::getName)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  public static Classifier fxSwapCurrencies() {
    var fxSwapCurrencies =
        FX_SWAP_PAIRS.stream().collect(groupingBy(CurrencyPair::getBase)).entrySet().stream()
            .sorted(
                comparingInt(
                    c -> {
                      int idx = ALLOWED_DISCOUNTING_CURRENCIES.indexOf(c.getKey());
                      return idx == -1
                          ? Integer
                              .MAX_VALUE // If it's not a discounting currency, put it at the end
                          : idx;
                    }))
            .map(
                e ->
                    new Classifier(
                        e.getKey().getCode(),
                        e.getValue().stream().map(v -> new Classifier(v.toString())).toList()))
            .toList();
    return new Classifier("fxSwapCurrencies", fxSwapCurrencies);
  }

  public static Classifier inflationCurveNames() {
    return new Classifier(
        "inflationCurveNames",
        InflationCurveConventions.INFLATION_CURVES.stream()
            .map(CurveConvention::getName)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  public static Classifier inflationCurveIndices() {
    return new Classifier(
        "inflationCurveIndices",
        InflationCurveConventions.INFLATION_CURVES.stream()
            .map(IndexCurveConvention::getIndex)
            .map(FloatingRateIndex::getName)
            .distinct()
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  public static Classifier inflationNodeConventions() {
    return new Classifier(
        "inflationCurveNodeConventions",
        InflationCurveConventions.INFLATION_CURVES.stream()
            .map(CoreClassifiersProvider::curveConventionClassifier)
            .toList());
  }

  public static Classifier inflationIndexConventions() {
    return new Classifier(
        "inflationIndexConventions",
        InflationCurveConventions.INFLATION_CURVES.stream()
            .map(IndexCurveConvention::getIndex)
            .map(FloatingRateIndex::getName)
            .distinct()
            .flatMap(
                index ->
                    findByIndexAndClearingHouse(index, NONE).stream()
                        .map(c -> new Classifier(index, nodeTypeClassifier(c))))
            .toList());
  }

  public static Classifier bondCurveNames() {
    return new Classifier(
        BOND_CURVE_NAMES_BY_CCY,
        BondCurveConventions.getCurves().stream()
            .map(CurveConvention::getCurrency)
            .distinct()
            .sorted()
            .map(
                ccy ->
                    new Classifier(
                        ccy.getCode(),
                        BondCurveConventions.getCurves().stream()
                            .filter(c -> c.getCurrency().equals(ccy))
                            .map(c -> new Classifier(c.getName()))
                            .toList()))
            .toList());
  }

  public static Classifier bondCurveNodeConventions() {
    return new Classifier(
        "bondCurveNodeConventions",
        BondCurveConventions.getCurves().stream()
            .map(bc -> new Classifier(bc.getName(), List.of(new Classifier(BOND_YIELD_NODE))))
            .toList());
  }

  public static Classifier indexNodeConventions() {
    return new Classifier(
        "indexCurveNodeConventions",
        IrIndexCurveConventions.INDEX_CURVES.stream()
            .map(CoreClassifiersProvider::curveConventionClassifier)
            .toList());
  }

  public static Classifier tenorBasisNodeConventions() {
    return new Classifier(
        "tenorBasisNodeConventions",
        IndexBasisCurveConventions.INDEX_BASIS_CURVES.stream()
            .map(CoreClassifiersProvider::curveConventionClassifier)
            .toList());
  }

  public static Classifier xccyNodeConventions() {
    return new Classifier(
        "xccyCurveNodeConventions",
        XccyCurveConventions.XCCY_CURVES.stream()
            .map(CoreClassifiersProvider::curveConventionClassifier)
            .toList());
  }

  public static Classifier valuationSettingsTypes() {
    return enumClassifier(
        "valuationSettingType", CompanySettingsType.class, CompanySettingsType::name);
  }

  public static Classifier curveConfigurationType() {
    return enumClassifier(
        "curveConfigurationType",
        CurveConfigurationType.class,
        CurveConfigurationType::name,
        CurveConfigurationType::getName);
  }

  private static Classifier calculationDiscountingType() {
    return enumClassifier(
        "calculationDiscountingType",
        CalculationDiscountingType.class,
        CalculationDiscountingType::name,
        CalculationDiscountingType::getLabel);
  }

  private static Classifier xvaCalculationDiscountingType() {
    return new Classifier(
        "xvaCalculationDiscountingType",
        Arrays.stream(CalculationDiscountingType.values())
            .filter(CalculationDiscountingType::isXvaApplicable)
            .map(r -> new Classifier(r.name(), r.getLabel()))
            .toList());
  }

  private static Classifier marketDataProviderType() {
    return sortByName(
        enumClassifier(
            "marketDataProviderType",
            DataProviderType.class,
            DataProviderType::name,
            DataProviderType::getLabel));
  }

  private static Classifier creditDocClauses() {
    return enumClassifier(DOC_CLAUSE_CLASSIFIER_NAME, CreditDocClause.class, CreditDocClause::name);
  }

  private static Classifier creditSectors() {
    return enumClassifier(
        SECTOR_CLASSIFIER_NAME, CreditSector.class, CreditSector::name, CreditSector::getLabel);
  }

  private static Classifier discountingCurrencies() {
    return new Classifier(
        "discountingCurrencies",
        ALLOWED_DISCOUNTING_CURRENCIES.stream().map(v -> new Classifier(v.toString())).toList());
  }

  private static Classifier indexedDiscountingCurrencies() {
    return new Classifier(
        "indexedDiscountingCurrencies",
        IndexBasedDiscountCurrencies.getAvailableDiscountCurrencies().stream()
            .map(v -> new Classifier(v.getName()))
            .toList());
  }

  private static Classifier currencyPair() {
    return new Classifier(
        CURRENCY_PAIR_CLASSIFIER,
        CurrencyPair.getAvailablePairs().stream()
            .map(v -> new Classifier(v.getBase() + "/" + v.getCounter()))
            .toList());
  }

  private static Classifier xccyFallbackCurrencies() {
    return new Classifier(
        "xccyFallbackCurrencies",
        XCCY_FALLBACK_CURRENCIES.stream().map(c -> new Classifier(c.getCode())).toList());
  }

  private static Classifier convexityAdjTenors() {
    return new Classifier(
        "convexityAdjTenors",
        Stream.of(TENOR_1M, TENOR_3M, TENOR_6M, TENOR_9M, TENOR_1Y, TENOR_18M, TENOR_2Y, TENOR_3Y)
            .map(v -> new Classifier(toStringNoPrefix(v.getPeriod())))
            .toList());
  }

  private static Classifier convexityAdjCurveNames() {
    return new Classifier(
        "convexityAdjCurveNames",
        ConventionalCurveConfigurations.ALL_CONVENTIONAL_CURVES.stream()
            .filter(c -> !c.nodeTypeConventions(IBOR_FUTURE_NODE).isEmpty())
            .map(CurveConvention::getName)
            .distinct()
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private static Classifier counterpartyType() {
    return enumClassifier("counterpartyType", CounterpartyType.class, CounterpartyType::name);
  }

  private static Classifier valuationStatus() {
    return enumClassifier("valuationStatus", ValuationStatus.class, ValuationStatus::name);
  }

  private static Classifier cdsIndex() {
    return enumClassifier(
        CREDIT_INDEX_CLASSIFIER_NAME, CdsIndex.class, CdsIndex::name, CdsIndex::getLabel);
  }

  private static Classifier creditTrancheIndices() {
    var classifiers =
        CreditTrancheIndices.PERMISSIBLE_VALUES.entrySet().stream()
            .map(
                i ->
                    new Classifier(
                        i.getKey().name(),
                        i.getKey().getLabel(),
                        convertCollectionTo(i.getValue(), Classifier::new)))
            .toList();
    return new Classifier(CREDIT_INDEX_TRANCHE_CLASSIFIER_NAME, classifiers);
  }

  private static Classifier allPermissibleTranches() {
    var classifiers =
        CreditTrancheIndices.ALL_TRANCHE_VALUES.stream()
            .sorted()
            .map(
                tranche ->
                    new Classifier(
                        tranche,
                        tranche,
                        permissibleIndicesForTranche(tranche).stream()
                            .map(CdsIndex::name)
                            .map(Classifier::new)
                            .toList()))
            .toList();
    return new Classifier("creditIndexTranche", classifiers);
  }

  private static Classifier creditType() {
    return enumClassifier(
        "creditType", CreditCurveType.class, CreditCurveType::name, CreditCurveType::getLabel);
  }

  private static List<Classifier> values(ExtendedEnum<?> extendedEnum) {
    return ClassifierUtils.values(extendedEnum, v -> new Classifier(v.toString()));
  }

  private static List<Classifier> availableTimeZones() {
    return Stream.concat(
            FIRST_TIME_ZONES.stream(),
            ZoneId.getAvailableZoneIds().stream()
                .filter(tz -> !FIRST_TIME_ZONES.contains(tz))
                .sorted())
        .map(Classifier::new)
        .toList();
  }

  private Classifier portfolioCalculationType() {
    return sortById(
        enumClassifier(
            "portfolioCalculationType",
            PortfolioCalculationType.class,
            PortfolioCalculationType::name));
  }

  private Classifier simulationsCount() {
    return new Classifier(
        "simulationsCount",
        Stream.of(3, 7, 15, 31, 63, 127, 255, 511, 1023, 2047, 4095, 8191, 16383)
            .map(v -> new Classifier(String.valueOf(v)))
            .toList());
  }

  private Classifier curveNodeClashAction() {
    return new Classifier(CURVE_NODE_CLASH_ACTION_CLASSIFIER, Constants.CURVE_NODE_CLASH_ACTIONS);
  }

  private Classifier stubConvention() {
    return sortById(
        enumClassifier(STUB_CONVENTION_CLASSIFIER, StubConvention.class, StubConvention::getName));
  }

  private Classifier overnightAccrualMethod() {
    return sortById(
        enumClassifier(
            OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER,
            OvernightAccrualMethod.class,
            OvernightAccrualMethod::getName));
  }

  private Classifier fixedAccrualMethod() {
    return sortById(
        enumClassifier(
            FIXED_ACCRUAL_METHOD_CLASSIFIER,
            FixedAccrualMethod.class,
            FixedAccrualMethod::getName));
  }

  private Classifier compoundingMethods() {
    return sortById(
        enumClassifier("compoundingMethods", CompoundingMethod.class, CompoundingMethod::getName));
  }

  private Classifier curveTypes() {
    return enumClassifier("curveTypes", CurveType.class, CurveType::name);
  }

  private Classifier priceIndexCalculationMethod() {
    return enumClassifier(
        "priceIndexCalculationMethod", PriceIndexCalculationMethod.class, NamedEnum::getName);
  }

  private Classifier exposureShiftType() {
    return new Classifier(
        "exposureShiftType",
        EXPOSURE_SHIFT_TYPES.stream()
            .map(v -> new Classifier(v.name(), v.getName()))
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier overridableOvernightIndexConventionsClassifier() {
    return new Classifier(
        "overridableOvernightIndexConventions",
        OverridableOvernightIndexes.defaultsByName.keySet().stream()
            .map(v -> new Classifier(v, v))
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier overridableFixedOvernightSwapConventionsClassifier() {
    return new Classifier(
        "overridableFixedOvernightSwapConventions",
        OverridableFixedOvernightSwapConventions.defaultsByName.keySet().stream()
            .map(v -> new Classifier(v, v))
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier scheduleConventions() {
    return new Classifier(
        "scheduleConventions",
        supportedBusinessDayConventions().stream().map(Classifier::new).toList());
  }

  private Classifier timeZones() {
    return new Classifier("timeZones", AVAILABLE_TIME_ZONES);
  }

  private Classifier curveNodeType() {
    return new Classifier(
        "curveNodeType",
        copyOf(CurveNodeTypes.VALUES).stream()
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier irInstruments() {
    return new Classifier(
        IR_INSTRUMENTS_CLASSIFIER_NAME,
        copyOf(CoreInstrumentType.values()).stream()
            .filter(i -> i.getInstrumentGroup() == CoreInstrumentGroup.IR)
            .map(type -> new Classifier(type.toString(), type.getLabel()))
            .toList());
  }

  private Classifier mdkRateInstruments() {
    return mdkInstruments("marketDataRateInstruments", CoreAssetGroup.RATES);
  }

  private Classifier mdkCreditInstruments() {
    return mdkInstruments("marketDataCreditInstruments", CoreAssetGroup.CREDIT);
  }

  private Classifier mdkFxRateInstruments() {
    return mdkInstruments("marketDataFxRateInstruments", CoreAssetGroup.FX);
  }

  private Classifier mdkInstruments(String classifierName, CoreAssetGroup group) {
    return new Classifier(
        classifierName,
        Arrays.stream(CoreInstrumentType.values())
            .filter(v -> v.assetClassGroup() == group)
            .map(i -> new Classifier(i.name(), i.getLabel()))
            .sorted(comparing((Classifier::getId)))
            .toList());
  }

  private Classifier curveExtrapolator() {
    return new Classifier(
        CURVE_EXTRAPOLATOR_CLASSIFIER,
        Constants.CURVE_EXTRAPOLATORS.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier dfInterpolatorExtrapolators() {
    return new Classifier(
        CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER,
        Stream.of(CurveExtrapolators.FLAT, CurveExtrapolators.LINEAR)
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier surfaceExtrapolator() {
    return new Classifier(
        SURFACE_EXTRAPOLATOR_CLASSIFIER,
        Constants.SURFACE_EXTRAPOLATORS.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier discountFactorCurveInterpolators() {
    var classifier =
        new Classifier(ExtendedCurveInterpolators.LOG_LINEAR_DISCOUNT_FACTOR.getName());
    return new Classifier(DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER, List.of(classifier));
  }

  private Classifier defaultCurveInterpolators() {
    return new Classifier(
        CURVE_INTERPOLATOR_CLASSIFIER,
        Constants.CURVE_INTERPOLATOR.stream()
            .map(ConventionMapper.INSTANCE::map)
            .filter(i -> !i.equals(ExtendedCurveInterpolators.LOG_LINEAR_DISCOUNT_FACTOR.getName()))
            .map(Classifier::new)
            .toList());
  }

  private Classifier productType() {
    return new Classifier(PRODUCT_TYPE, null, coreProductType().getValues(), ProductType.class);
  }

  private Classifier productTypeGroup() {
    return new Classifier(PRODUCT_TYPE_GROUP, null, productTypeGroups(), ProductGroup.class);
  }

  private Classifier coreProductType() {
    return enumClassifier(
        "coreProductType", CoreProductType.class, CoreProductType::name, CoreProductType::label);
  }

  private Classifier coreProductTypeGroup() {
    return new Classifier(
        "coreProductTypeGroup", null, productTypeGroups(), CoreProductTypeGroup.class);
  }

  private List<Classifier> productTypeGroups() {
    return Stream.of(CoreProductTypeGroup.values())
        .map(
            group ->
                new Classifier(
                    group.name(),
                    group.getProductTypes().stream()
                        .map(c -> new Classifier(c.name(), c.label()))
                        .toList()))
        .toList();
  }

  private Classifier breakTestThresholdLevel() {
    return enumClassifier(
        BREAK_TEST_THRESHOLD_LEVEL,
        CoreThresholdLevel.class,
        CoreThresholdLevel::name,
        CoreThresholdLevel::label);
  }

  private Classifier position() {
    return sortById(enumClassifier("position", PositionType.class, PositionType::name));
  }

  private Classifier fxLongShort() {
    return new Classifier(
        "fxLongShort",
        Stream.of(FxLongShort.values())
            .map(v -> new Classifier(v.name()))
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier callPutType() {
    return sortById(enumClassifier("callPutType", CallPutType.class, CallPutType::name));
  }

  private Classifier capFloorType() {
    return sortById(enumClassifier("capFloorType", CapFloorType.class, CapFloorType::name));
  }

  private Classifier rollConvention() {
    var conventions =
        RollConvention.extendedEnum().lookupAllNormalized().values().stream()
            .filter(not(NthWeekdayOfMonthRollConvention.class::isInstance))
            .toList();

    return new Classifier(
        ROLL_CONVENTION_CLASSIFIER,
        valuesStream(v -> new Classifier(ConventionMapper.INSTANCE.map(v)), conventions).toList());
  }

  private Classifier currency() {
    return new Classifier(
        CURRENCY_CLASSIFIER,
        EXPLICIT_CURRENCIES.stream()
            .sorted(CurrencyComparator.INSTANCE)
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier iborIndex() {
    return new Classifier(
        "iborIndex",
        IBOR_INDICES.stream().map(ConventionMapper.INSTANCE::map).map(Classifier::new).toList());
  }

  private Classifier iborIndexByCcy() {
    return new Classifier("iborIndexByCurrency", indiciesByCcy(IBOR_INDICES));
  }

  private Classifier capFloorTradeIborIndexByCcy() {
    return new Classifier("capFloorTradeIborIndexByCcy", indiciesByCcy(Constants.CAPFLOOR_INDICES));
  }

  private Classifier swaptionTradeIborIndexByCcy() {
    return new Classifier("swaptionTradeIborIndexByCcy", indiciesByCcy(Constants.SWAPTION_INDICES));
  }

  private Classifier overnightIndexByCcy() {
    return new Classifier("overnightIndexByCurrency", indiciesByCcy(Constants.OVERNIGHT_INDICES));
  }

  private Classifier inflationIndexByCcy() {
    return new Classifier("inflationIndexByCurrency", indiciesByCcy(Constants.INFLATION_INDICES));
  }

  private Classifier overnightTermIndexByCcy() {
    return new Classifier(
        "overnightTermIndexByCurrency", indiciesByCcy(Constants.OVERNIGHT_TERM_INDICES));
  }

  private Classifier iborIndexDiscountEur() {
    return new Classifier(
        "iborIndexDiscountEur",
        IBOR_INDICES_DISCOUNT_EUR.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountUsd() {
    return new Classifier(
        "iborIndexDiscountUsd",
        IBOR_INDICES_DISCOUNT_USD.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountGbp() {
    return new Classifier(
        "iborIndexDiscountGbp",
        IBOR_INDICES_DISCOUNT_GBP.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountAud() {
    return new Classifier(
        "iborIndexDiscountAud",
        IBOR_INDICES_DISCOUNT_AUD.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountCad() {
    return new Classifier(
        "iborIndexDiscountCad",
        IBOR_INDICES_DISCOUNT_CAD.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountChf() {
    return new Classifier(
        "iborIndexDiscountChf",
        IBOR_INDICES_DISCOUNT_CHF.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountJpy() {
    return new Classifier(
        "iborIndexDiscountJpy",
        IBOR_INDICES_DISCOUNT_JPY.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier iborIndexDiscountNzd() {
    return new Classifier(
        "iborIndexDiscountNzd",
        IBOR_INDICES_DISCOUNT_NZD.stream().map(v -> new Classifier(v.getName())).toList());
  }

  private Classifier priceIndex() {
    return new Classifier("priceIndex", null, values(PriceIndex.extendedEnum()), PriceIndex.class);
  }

  private Classifier overnightIndex() {
    return new Classifier(
        "overnightIndex", null, values(OvernightIndex.extendedEnum()), OvernightIndex.class);
  }

  private Classifier fxIndex() {
    return new Classifier("fxIndex", null, values(FxIndex.extendedEnum()), FxIndex.class);
  }

  private Classifier swapLegFrequency() {
    return new Classifier(
        "swapLegFrequency",
        SWAP_LEG_FREQUENCIES.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .sorted(comparing(Classifier::getId))
            .toList());
  }

  private Classifier fraSettlement() {
    return new Classifier(
        "fraSettlement",
        FRA_SETTLEMENT_VALUES.stream().sorted().map(v -> new Classifier(v.toString())).toList());
  }

  private Classifier creditCurveSeniority() {
    return enumClassifier(
        CREDIT_SENIORITY_CLASSIFIER, CreditSeniority.class, CreditSeniority::name);
  }

  private Classifier serialFuture() {
    return new Classifier(
        "serialFuture", SERIAL_FUTURE_VALUES.stream().map(Classifier::new).toList());
  }

  private Classifier curveStrippingProductPriorities() {
    return sortById(
        enumClassifier(
            "curveStrippingProductPriorities",
            CurveStrippingProductPriority.class,
            CurveStrippingProductPriority::name));
  }

  private Classifier volatilitySwapConventions() {
    return new Classifier(
        "volatilitySwapConvention",
        VOLATILITY_SURFACES.stream()
            .map(VolatilitySurfaceConfiguration::getSwapConvention)
            .map(Classifier::new)
            .toList());
  }

  private Classifier strippingType() {
    return enumClassifier(
        STRIPPING_TYPE_CLASSIFIER,
        CalculationStrippingType.class,
        CalculationStrippingType::name,
        CalculationStrippingType::getLabel);
  }

  private Classifier creditFrequency() {
    return new Classifier(
        CREDIT_FREQUENCY_CLASSIFIER,
        Constants.CREDIT_FREQUENCIES.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier minNodeGaps() {
    return new Classifier(
        MIN_NODE_GAP_CLASSIFIER,
        Constants.MIN_NODE_GAPS.stream()
            .map(ConventionMapper.INSTANCE::map)
            .map(Classifier::new)
            .toList());
  }

  private Classifier tradeCalculationType() {
    return enumClassifier(
        "tradeCalculationType",
        CalculationType.class,
        CalculationType::name,
        CalculationType::getLabel);
  }

  private Classifier capletValuationModel() {
    return enumClassifier(
        CAPLET_VALUATION_MODEL_CLASSIFIER,
        CapletValuationModel.class,
        CapletValuationModel::name,
        CapletValuationModel::getLabel);
  }

  private Classifier volatilitySurfaceType() {
    return enumClassifier(
        VOLATILITY_TYPE_CLASSIFIER,
        VolatilitySurfaceType.class,
        VolatilitySurfaceType::name,
        VolatilitySurfaceType::getLabel);
  }

  private Classifier swaptionSettlementType() {
    return new Classifier(SWAPTION_SETTLEMENT_TYPE_CLASSIFIER, Constants.SWAPTION_SETTLEMENT_TYPES);
  }

  private Classifier defaultCurveValueType() {
    return new Classifier(DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER, Constants.DEFAULT_CURVE_VALUE_TYPES);
  }

  private Classifier inflationCurveValueType() {
    return new Classifier(
        INFLATION_CURVE_VALUE_TYPE_CLASSIFIER, Constants.INFLATION_CURVE_VALUE_TYPES);
  }

  private Classifier businessDayAdjustmentType() {
    return enumClassifier(
        BUSINESS_DAY_ADJUSTMENT_TYPE,
        BusinessDayAdjustmentType.class,
        BusinessDayAdjustmentType::name,
        BusinessDayAdjustmentType::getLabel);
  }

  private Classifier inflationBusinessDayAdjustmentType() {
    var inflationAdjustmentTypeClassifier =
        new Classifier(PAYMENT_ONLY.name(), PAYMENT_ONLY.getLabel());
    return new Classifier(
        INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE, List.of(inflationAdjustmentTypeClassifier));
  }

  public static Classifier supportedCalendars() {
    var calendars =
        Constants.SUPPORTED_CALENDARS.stream()
            .distinct()
            .map(v -> new Classifier(v.getName()))
            .sorted(comparing(Classifier::getId))
            .toList();
    return new Classifier(SUPPORTED_CALENDARS_CLASSIFIER, calendars);
  }

  public static Classifier loanNoteDayCounts() {
    var dayCounts =
        SupportedDayCount.supportedLoanDayCounts().stream()
            .map(c -> new Classifier(c.dayCountName(), c.label()))
            .sorted(comparing(Classifier::getId))
            .toList();
    return new Classifier(LOAN_NOTE_DAY_COUNTS, dayCounts);
  }

  public static Classifier jpLoanNoteDayCounts() {
    var dayCounts =
        SupportedDayCount.supportedJpStandardDayCounts().stream()
            .map(c -> new Classifier(c.dayCountName(), c.label()))
            .sorted(comparing(Classifier::getId))
            .toList();
    return new Classifier(JP_LOAN_NOTE_DAY_COUNTS, dayCounts);
  }

  private Classifier mdkProviderBidAskTypes() {
    return enumClassifier(
        MDK_BIDASK_TYPES,
        MdkProviderBidAskType.class,
        MdkProviderBidAskType::name,
        MdkProviderBidAskType::getLabel);
  }

  private Classifier mdValueBidAskTypes() {
    return enumClassifier(
        MD_VALUE_BIDASK_TYPES,
        ValueBidAskType.class,
        ValueBidAskType::name,
        ValueBidAskType::getLabel);
  }

  private static Classifier instrumentPriceTypes() {
    return enumClassifier(
        INSTRUMENT_PRICE_TYPES,
        InstrumentPriceType.class,
        InstrumentPriceType::name,
        InstrumentPriceType::getLabel);
  }

  private Classifier pricingSlot() {
    return enumClassifier(
        PRICING_SLOTS, PricingSlot.class, PricingSlot::name, PricingSlot::getLabel);
  }

  private Classifier slaDeadline() {
    return enumClassifier(
        SLA_DEADLINES, SlaDeadline.class, SlaDeadline::name, SlaDeadline::getLabel);
  }
}
