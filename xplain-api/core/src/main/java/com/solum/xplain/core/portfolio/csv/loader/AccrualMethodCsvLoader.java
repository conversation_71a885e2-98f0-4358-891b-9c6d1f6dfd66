package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.FIXED_ACCRUAL_METHOD_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_FIXED_ACCRUAL_METHOD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_OVERNIGHT_ACCRUAL_METHOD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_ACCRUAL_METHOD;

import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.solum.xplain.core.classifiers.conventions.Leg;
import com.solum.xplain.core.classifiers.conventions.SwapConvention;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.portfolio.value.CalculationType;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AccrualMethodCsvLoader {
  private static final ClassifierSupplier FIXED_ACCRUALS_SUPPLIER =
      new ClassifierSupplier(FIXED_ACCRUAL_METHOD_CLASSIFIER);
  private static final ClassifierSupplier OVERNIGHT_ACCRUALS_SUPPLIER =
      new ClassifierSupplier(OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER);

  public static FixedAccrualMethod parseFixedAccrualMethod(
      CsvRow row, String parent, @Nullable SwapConvention convention) {
    var defaultAccrualMethod =
        Optional.ofNullable(convention)
            .flatMap(v -> v.legByType(CalculationType.FIXED))
            .map(Leg::getAccrualMethod)
            .map(FixedAccrualMethod::of)
            .orElse(FixedAccrualMethod.DEFAULT);
    return row.findValue(addField(parent, CALCULATION_FIXED_ACCRUAL_METHOD))
        .map(v -> validateValue(v, FIXED_ACCRUALS_SUPPLIER))
        .map(FixedAccrualMethod::of)
        .orElse(defaultAccrualMethod);
  }

  public static OvernightAccrualMethod parseOvernightAccrualMethod(
      CsvRow row, String parent, @Nullable SwapConvention convention) {
    var defaultAccrualMethod =
        Optional.ofNullable(convention)
            .flatMap(c -> c.legByType(CalculationType.OVERNIGHT))
            .map(Leg::getAccrualMethod)
            .map(OvernightAccrualMethod::of)
            .orElse(OvernightAccrualMethod.COMPOUNDED);
    return row.findValue(addField(parent, CALCULATION_OVERNIGHT_ACCRUAL_METHOD))
        .map(v -> validateValue(v, OVERNIGHT_ACCRUALS_SUPPLIER))
        .map(OvernightAccrualMethod::of)
        .orElse(defaultAccrualMethod);
  }

  // TODO: SXSD-10515 OG - Extend RateCalculation to new leg type
  // We use overnight accrual method but will move to overnight term when we have implementation in
  // OG
  public static OvernightAccrualMethod parseOvernightTermAccrualMethod(
      CsvRow row, String parent, @Nullable SwapConvention convention) {
    var defaultAccrualMethod =
        Optional.ofNullable(convention)
            .flatMap(c -> c.legByType(CalculationType.TERM_OVERNIGHT))
            .map(Leg::getAccrualMethod)
            .map(OvernightAccrualMethod::of)
            .orElse(OvernightAccrualMethod.COMPOUNDED);
    return row.findValue(addField(parent, CALCULATION_TERM_OVERNIGHT_ACCRUAL_METHOD))
        .map(v -> validateValue(v, OVERNIGHT_ACCRUALS_SUPPLIER))
        .map(OvernightAccrualMethod::of)
        .orElse(defaultAccrualMethod);
  }
}
