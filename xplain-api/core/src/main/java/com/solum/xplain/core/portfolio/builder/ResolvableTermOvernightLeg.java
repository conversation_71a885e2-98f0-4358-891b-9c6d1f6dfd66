package com.solum.xplain.core.portfolio.builder;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.utils.FrequencyUtils;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

@Builder
@ToString
@Getter
@EqualsAndHashCode
public class ResolvableTermOvernightLeg implements ResolvableTradeLegDetails {
  @NonNull private final String index;
  @NonNull private final Currency currency;
  @Nullable private final Double initialValue;
  @NonNull private final PayReceive payReceive;
  @NonNull private final Double notional;
  @NonNull private final Frequency accrualFrequency;
  @NonNull private final String paymentCompounding;
  @NonNull private final OvernightAccrualMethod overnightAccrualMethod;
  @NonNull private final Frequency paymentFrequency;
  @Nullable private final String startDateBusinessDayAdjustment;
  @Nullable private final String endDateBusinessDayAdjustment;
  @NonNull private final Integer rateCutOffDays;
  @NonNull private final Integer paymentOffsetDays;
  @NonNull private final Integer fixingDateOffset;
  @Nullable private final String fixingRelativeTo;
  private final boolean notionalExchange;
  private final DayCount dayCount;
  private final String extLegIdentifier;

  @Override
  public TradeLegDetails toTradeLegDetails() {
    TradeLegDetails legDetails = new TradeLegDetails();
    legDetails.setType(getCalculationType());
    legDetails.setCurrency(currency.getCode());
    legDetails.setDayCount(dayCount.getName());
    legDetails.setIndex(index);
    legDetails.setExtLegIdentifier(extLegIdentifier);
    legDetails.setFixingDateOffsetDays(fixingDateOffset);
    legDetails.setAccrualMethod(overnightAccrualMethod.getName());
    legDetails.setOvernightRateCutOffDays(rateCutOffDays);
    legDetails.setInitialValue(initialValue);
    legDetails.setPayReceive(payReceive);
    legDetails.setNotional(notional);
    legDetails.setAccrualFrequency(FrequencyUtils.toStringNoPrefix(accrualFrequency));
    legDetails.setPaymentFrequency(FrequencyUtils.toStringNoPrefix(paymentFrequency));
    legDetails.setPaymentCompounding(paymentCompounding);
    legDetails.setPaymentOffsetDays(paymentOffsetDays);
    return legDetails;
  }

  @Override
  public CalculationType getCalculationType() {
    return CalculationType.TERM_OVERNIGHT;
  }
}
