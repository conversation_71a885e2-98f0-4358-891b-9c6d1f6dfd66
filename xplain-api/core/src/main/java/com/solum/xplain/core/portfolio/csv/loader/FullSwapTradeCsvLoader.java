package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.classifiers.Constants.IBOR_INDICES;
import static com.solum.xplain.core.classifiers.Constants.INFLATION_INDICES;
import static com.solum.xplain.core.classifiers.Constants.OVERNIGHT_INDICES;
import static com.solum.xplain.core.classifiers.Constants.OVERNIGHT_TERM_INDICES;
import static com.solum.xplain.core.classifiers.Constants.SWAPTION_INDICES;
import static com.solum.xplain.core.classifiers.PermissibleConventions.FIXED_INFLATION_CONVENTION_BY_INDEX;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parsePayReceive;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateNonNegativeInteger;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validatePositiveValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.ACRUAL_SCHEDULE_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_FIXED_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_FIXED_RATE_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_FIXING_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_SPREAD_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_INFLATION_CALCULATION_METHOD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_INFLATION_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_INFLATION_LAG;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_OVERNIGHT_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_OVERNIGHT_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_OVERNIGHT_SPREAD_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_FIXING_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_FIXING_RELATIVE_TO;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_INDEX;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_NotionalExchange;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_RATE_CUTOFF_DAYS;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_TERM_OVERNIGHT_SPREAD_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_IDENTIFIER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.OFFSHORE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_COMPOUNDING_METHOD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_DATE_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAYMENT_SCHEDULE_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_NOTIONAL_SCHEDULE_FINAL_EXCHANGE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_NOTIONAL_SCHEDULE_INITIAL_EXCHANGE;
import static com.solum.xplain.core.portfolio.csv.SwapLegsValidator.validateLegs;
import static com.solum.xplain.core.portfolio.csv.loader.AccrualMethodCsvLoader.parseFixedAccrualMethod;
import static com.solum.xplain.core.portfolio.csv.loader.AccrualMethodCsvLoader.parseOvernightAccrualMethod;
import static com.solum.xplain.core.portfolio.csv.loader.AccrualMethodCsvLoader.parseOvernightTermAccrualMethod;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.BUSINESS_DAY_ADJUSTMENT_TYPE_SUPPLIER;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.rollConvention;
import static com.solum.xplain.core.portfolio.validation.OffshoreIndexValidator.validateIborOffshoreIndex;
import static com.solum.xplain.core.portfolio.validation.OffshoreIndexValidator.validateOvernightOffshoreIndex;
import static com.solum.xplain.core.portfolio.validation.ValidCompoundingMethodValidator.INVALID_COMPOUNDING;
import static com.solum.xplain.core.portfolio.validation.ValidCompoundingMethodValidator.validateCompoundingMethod;
import static com.solum.xplain.core.utils.FrequencyUtils.toStringNoPrefix;
import static com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies.indexAccrualFrequency;
import static com.solum.xplain.extensions.enums.BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT;
import static io.atlassian.fugue.Either.left;
import static java.lang.String.format;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.PaymentRelativeTo;
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.classifiers.conventions.SwapConvention;
import com.solum.xplain.core.classifiers.conventions.SwapLegKey;
import com.solum.xplain.core.classifiers.conventions.SwapTradeConventions;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle;
import com.solum.xplain.core.common.validation.ValidPeriodValidator;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableInflationLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableOvernightLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableSwapDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTermOvernightLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/** Loads Swap trades from CSV files. */
@Component
public class FullSwapTradeCsvLoader {

  private static final ClassifierSupplier INDEX_CALC_METHOD_SUPPLIER =
      new ClassifierSupplier("priceIndexCalculationMethod");

  private static final ClassifierSupplier INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE_SUPPLIER =
      new ClassifierSupplier(INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE);

  private static final String INVALID_PRICE_INDEX = "Convention for %s could not be found!";
  private static final String INVALID_CURRENCY = "Invalid currency %s for index %s";
  private static final String INVALID_LAG_INTEGER =
      "Inflation lag must be positive number or valid Y/M tenor";

  /**
   * Parses from the CSV row.
   *
   * @param row the CSV row
   * @param refSecTrade true if this is a reference security trade we are loading
   * @return the parsed trade
   */
  public Either<ErrorItem, ResolvableSwapDetails> parse(
      CsvRow row, CoreProductType productType, boolean refSecTrade) {
    try {
      var builder = parseCommonFields(row, productType);
      List<ResolvableTradeLegDetails> legs = new ArrayList<>();

      if (getValue(row, LEG_1, PAY_RECEIVE) != null) {
        legs.add(parseLeg(row, LEG_1, productType));
      }

      if (getValue(row, LEG_2, PAY_RECEIVE) != null) {
        legs.add(parseLeg(row, LEG_2, productType));
      }

      return validateLegs(
              productType, null, legs, row) // Position type is only required for FRA validation.
          .map(builder::withLegs)
          .map(ResolvableSwapDetails.ResolvableSwapDetailsBuilder::build);
    } catch (RuntimeException ex) {
      return left(
          PARSING_ERROR.entity(
              format("Error at line number %s. Error: %s", row.lineNumber(), ex.getMessage())));
    }
  }

  // Default implementation for single ccy swaps where both legs have same currency.
  public Currency tradeCurrency(CsvRow row) {
    return CsvLoaderUtils.parseTradeCurrency(row).orElseGet(() -> legCurrency(row, LEG_1));
  }

  private ResolvableSwapDetails.ResolvableSwapDetailsBuilder parseCommonFields(
      CsvRow row, CoreProductType productType) {
    var accrualDates = TradeCsvLoaderUtils.parseAccrualDates(row);

    var builder =
        ResolvableSwapDetails.builder()
            .startDate(accrualDates.getStartDate())
            .endDate(accrualDates.getEndDate())
            .businessDayConvention(businessDayConvention(row))
            .businessDayAdjustmentType(businessDayAdjustmentType(row, productType))
            .rollConvention(rollConvention(row))
            .firstRegularStartDate(accrualDates.getRegularStartDate())
            .lastRegularEndDate(accrualDates.getRegularEndDate())
            .stubConvention(accrualDates.getStubConvention());

    row.findValue(TRADE_NOTIONAL_SCHEDULE_INITIAL_EXCHANGE)
        .map(CsvLoaderUtils::parseBoolean)
        .ifPresent(builder::notionalScheduleInitialExchange);

    row.findValue(TRADE_NOTIONAL_SCHEDULE_FINAL_EXCHANGE)
        .map(CsvLoaderUtils::parseBoolean)
        .ifPresent(builder::notionalScheduleFinalExchange);

    return builder;
  }

  String businessDayConvention(CsvRow row) {
    return TradeCsvLoaderUtils.parseBusinessDayConvention(row, TRADE_BUSINESS_DAY_CONVENTION)
        .getName();
  }

  private BusinessDayAdjustmentType businessDayAdjustmentType(
      CsvRow row, CoreProductType productType) {
    String enumStr;
    if (productType == CoreProductType.INFLATION) {
      enumStr =
          row.findValue(TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE)
              .map(val -> validateValue(val, INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE_SUPPLIER))
              .orElse(BusinessDayAdjustmentType.PAYMENT_ONLY.name());
    } else {
      enumStr =
          row.findValue(TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE)
              .map(val -> validateValue(val, BUSINESS_DAY_ADJUSTMENT_TYPE_SUPPLIER))
              .orElse(ACCRUAL_AND_PAYMENT.name());
    }
    return BusinessDayAdjustmentType.valueOf(enumStr);
  }

  private ResolvableTradeLegDetails parseLeg(CsvRow row, String leg, CoreProductType productType) {
    var calculationType = parseRateCalculationType(leg, row);
    return switch (calculationType) {
      case FIXED -> parseFixedLeg(row, leg, productType);
      case IBOR -> parseIborLeg(row, leg, productType);
      case OVERNIGHT -> parseOvernightLeg(row, leg, productType);
      case INFLATION -> parseInflationLeg(row, leg, productType);
      case TERM_OVERNIGHT -> parseTermOvernightLeg(row, leg, productType);
    };
  }

  CalculationType parseRateCalculationType(String leg, CsvRow row) {
    var typeStr = row.getValue(addField(leg, LEG_TYPE));
    return CalculationType.valueOf(typeStr.toUpperCase());
  }

  private ResolvableTradeLegDetails parseFixedLeg(
      CsvRow row, String leg, CoreProductType productType) {
    var identifier = legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var accrualFreq = accrualFreq(row, leg);
    var paymentFreq = paymentFreq(row, leg);
    var swapConvention = parseSwapConvention(row, productType);
    var dayCount = row.getValue(addField(leg, CALCULATION_FIXED_DAY_COUNT));
    var initialValue = CsvLoaderUtils.parseDouble(row, addField(leg, CALCULATION_FIXED_RATE_VALUE));

    return ResolvableFixedLeg.builder()
        .extLegIdentifier(identifier)
        .dayCount(
            CsvLoaderUtils.parseDefaultDayCount(
                dayCount, addField(leg, CALCULATION_FIXED_DAY_COUNT)))
        .initialValue(initialValue)
        .notional(notional(row, leg, productType))
        .currency(legCurrency(row, leg))
        .paymentCompounding(paymentCompounding(row, leg, paymentFreq, accrualFreq))
        .accrualFrequency(accrualFreq)
        .accrualMethod(parseFixedAccrualMethod(row, leg, swapConvention))
        .payReceive(payReceive(row, leg))
        .paymentFrequency(paymentFreq)
        .paymentOffsetDays(paymentOffset(row, leg))
        .build();
  }

  ResolvableTradeLegDetails parseOvernightLeg(CsvRow row, String leg, CoreProductType productType) {
    var identifier = legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var index = overnightIndex(row, leg);
    var accrualFreq = accrualFreq(row, leg);
    var paymentFreq = paymentFreq(row, leg);
    var swapConvention = parseSwapConvention(row, productType);
    var accrualMethod = parseOvernightAccrualMethod(row, leg, swapConvention);
    var dayCount =
        row.findValue(addField(leg, CALCULATION_OVERNIGHT_DAY_COUNT))
            .map(
                v ->
                    CsvLoaderUtils.parseDefaultDayCount(
                        v, addField(leg, CALCULATION_OVERNIGHT_DAY_COUNT)))
            .orElse(index.getDayCount());
    var initialSpread =
        row.findValue(addField(leg, CALCULATION_OVERNIGHT_SPREAD_VALUE))
            .map(CsvLoaderUtils::parseDouble)
            .orElse(null);
    var rateCutOffDaysField = addField(leg, CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS);
    var rateCutOffDays =
        row.findValue(rateCutOffDaysField)
            .map(Integer::parseInt)
            .map(i -> validateNonNegativeInteger(i, rateCutOffDaysField))
            .orElse(0);

    return ResolvableOvernightLeg.builder()
        .extLegIdentifier(identifier)
        .index(index.getName())
        .initialValue(initialSpread)
        .dayCount(dayCount)
        .notional(notional(row, leg, productType))
        .currency(indexCurrency(row, leg, index))
        .paymentCompounding(paymentCompounding(row, leg, paymentFreq, accrualFreq))
        .accrualFrequency(accrualFreq)
        .payReceive(payReceive(row, leg))
        .paymentFrequency(paymentFreq)
        .paymentOffsetDays(paymentOffset(row, leg))
        .rateCutOffDays(rateCutOffDays)
        .overnightAccrualMethod(accrualMethod)
        .isOffshore(offshoreLeg(row, leg, () -> validateOvernightOffshoreIndex(index.getName())))
        .build();
  }

  private OvernightIndex overnightIndex(CsvRow row, String leg) {
    var indexStr = row.getValue(addField(leg, CALCULATION_OVERNIGHT_INDEX));
    var index = OvernightIndex.of(indexStr);
    return validateValue(index, OVERNIGHT_INDICES);
  }

  private ResolvableTradeLegDetails parseIborLeg(
      CsvRow row, String leg, CoreProductType productType) {
    var identifier = legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var index = iborIndex(row, leg, productType);
    var permissibleAccrualFreq = indexAccrualFrequency(index);
    var accrualFreqField = addField(leg, ACRUAL_SCHEDULE_FREQUENCY);
    var paymentFreqField = addField(leg, PAYMENT_SCHEDULE_FREQUENCY);
    var accrualFreq =
        parseIborFrequency(
            row,
            ACRUAL_SCHEDULE_FREQUENCY,
            accrualFreqField,
            index,
            Set.of(permissibleAccrualFreq));
    var paymentFreq =
        parseIborFrequency(
            row,
            PAYMENT_SCHEDULE_FREQUENCY,
            paymentFreqField,
            index,
            Constants.SWAP_LEG_FREQUENCIES);
    var dayCount =
        row.findValue(addField(leg, CALCULATION_IBOR_DAY_COUNT))
            .map(
                v ->
                    CsvLoaderUtils.parseDefaultDayCount(
                        v, addField(leg, CALCULATION_IBOR_DAY_COUNT)))
            .orElse(index.getDayCount());
    var initialSpread =
        row.findValue(addField(leg, CALCULATION_IBOR_SPREAD_VALUE))
            .map(CsvLoaderUtils::parseDouble)
            .orElse(null);
    var fixingOffsetField = addField(leg, CALCULATION_IBOR_FIXING_OFFSET);
    var fixingDateOffset =
        row.findValue(fixingOffsetField)
            .map(Integer::parseInt)
            .map(v -> CsvLoaderUtils.validateNonPositiveInteger(v, fixingOffsetField))
            .orElse(index.getFixingDateOffset().getDays());

    return ResolvableIborLeg.builder()
        .extLegIdentifier(identifier)
        .index(index.getName())
        .initialValue(initialSpread)
        .dayCount(dayCount)
        .fixingDateOffset(fixingDateOffset)
        .notional(notional(row, leg, productType))
        .currency(indexCurrency(row, leg, index))
        .paymentCompounding(paymentCompounding(row, leg, paymentFreq, accrualFreq))
        .accrualFrequency(accrualFreq)
        .payReceive(payReceive(row, leg))
        .paymentFrequency(paymentFreq)
        .paymentOffsetDays(paymentOffset(row, leg))
        .isOffshore(offshoreLeg(row, leg, () -> validateIborOffshoreIndex(index.getName())))
        .build();
  }

  ResolvableTradeLegDetails parseInflationLeg(CsvRow row, String leg, CoreProductType productType) {
    var identifier = legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var priceIndex = priceIndex(row, leg);
    var accrualFreq = accrualFreq(row, leg);
    var paymentFreq = paymentFreq(row, leg);
    var convention =
        Optional.ofNullable(FIXED_INFLATION_CONVENTION_BY_INDEX.get(priceIndex))
            .orElseThrow(
                () -> new IllegalArgumentException(String.format(INVALID_PRICE_INDEX, priceIndex)));

    var inflationLagFieldName = addField(leg, CALCULATION_INFLATION_LAG);
    var inflationLag =
        row.findValue(inflationLagFieldName)
            .map(val -> parseInflationLag(val, inflationLagFieldName))
            .orElse(convention.getLag());

    var methodStr = addField(leg, CALCULATION_INFLATION_CALCULATION_METHOD);
    var calculationMethod =
        parseCalculationMethod(row, methodStr, convention.getIndexCalculationMethod());

    return ResolvableInflationLeg.builder()
        .extLegIdentifier(identifier)
        .index(priceIndex.getName())
        .inflationLag(toStringNoPrefix(inflationLag))
        .indexCalculationMethod(calculationMethod.toString())
        .notional(notional(row, leg, productType))
        .currency(indexCurrency(row, leg, priceIndex))
        .paymentCompounding(paymentCompounding(row, leg, paymentFreq, accrualFreq))
        .accrualFrequency(accrualFreq)
        .payReceive(payReceive(row, leg))
        .paymentFrequency(paymentFreq)
        .paymentOffsetDays(paymentOffset(row, leg))
        .build();
  }

  ResolvableTradeLegDetails parseTermOvernightLeg(
      CsvRow row, String leg, CoreProductType productType) {
    var identifier = legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var index = termOvernightIndex(row, leg);
    var accrualFreq = accrualFreq(row, leg);
    var paymentFreq = paymentFreq(row, leg);
    var dayCount =
        row.findValue(addField(leg, CALCULATION_TERM_OVERNIGHT_DAY_COUNT))
            .map(
                v ->
                    CsvLoaderUtils.parseDefaultDayCount(
                        v, addField(leg, CALCULATION_TERM_OVERNIGHT_DAY_COUNT)))
            .orElse(DayCount.of("Act/360"));
    var initialSpread =
        row.findValue(addField(leg, CALCULATION_TERM_OVERNIGHT_SPREAD_VALUE))
            .map(CsvLoaderUtils::parseDouble)
            .orElse(null);
    var fixingOffsetField = addField(leg, CALCULATION_TERM_OVERNIGHT_FIXING_OFFSET);
    var fixingDateOffset =
        row.findValue(fixingOffsetField)
            .map(Integer::parseInt)
            .map(v -> CsvLoaderUtils.validateNonPositiveInteger(v, fixingOffsetField))
            .orElse(-2);
    var accrualDates = TradeCsvLoaderUtils.parseAccrualDates(row);
    var rateCutOffDaysField = addField(leg, CALCULATION_TERM_OVERNIGHT_RATE_CUTOFF_DAYS);
    var rateCutOffDays =
        row.findValue(rateCutOffDaysField)
            .map(Integer::parseInt)
            .map(i -> validateNonNegativeInteger(i, rateCutOffDaysField))
            .orElse(0);
    var swapConvention = parseSwapConvention(row, productType);
    var accrualMethod = parseOvernightTermAccrualMethod(row, leg, swapConvention);
    var fixingRelativeTo =
        row.findValue(addField(leg, CALCULATION_TERM_OVERNIGHT_FIXING_RELATIVE_TO))
            .orElse(PaymentRelativeTo.PERIOD_START.toString());
    var notionalExchange =
        row.findValue(addField(leg, CALCULATION_TERM_OVERNIGHT_NotionalExchange))
            .map(Boolean::parseBoolean)
            .orElse(false);

    return ResolvableTermOvernightLeg.builder()
        .extLegIdentifier(identifier)
        .index(index.getName())
        .currency(legCurrency(row, leg))
        .initialValue(initialSpread)
        .payReceive(payReceive(row, leg))
        .notional(notional(row, leg, productType))
        .accrualFrequency(accrualFreq)
        .paymentCompounding(paymentCompounding(row, leg, paymentFreq, accrualFreq))
        .overnightAccrualMethod(accrualMethod)
        .paymentFrequency(paymentFreq)
        .startDateBusinessDayAdjustment("")
        .endDateBusinessDayAdjustment("")
        .rateCutOffDays(rateCutOffDays)
        .paymentOffsetDays(paymentOffset(row, leg))
        .fixingDateOffset(fixingDateOffset)
        .fixingRelativeTo(fixingRelativeTo)
        .notionalExchange(notionalExchange)
        .dayCount(dayCount)
        .build();
  }

  private OvernightTermIndex termOvernightIndex(CsvRow row, String leg) {
    var indexStr = row.getValue(addField(leg, CALCULATION_TERM_OVERNIGHT_INDEX));
    var index = OvernightTermIndex.of(indexStr);
    return validateValue(index, OVERNIGHT_TERM_INDICES);
  }

  String legIdentifier(CsvRow row, String fieldName) {
    var legIdentifier = row.findValue(fieldName);
    return legIdentifier.map(id -> parseIdentifier(row, fieldName)).orElse(null);
  }

  double notional(CsvRow row, String leg, CoreProductType productType) {
    double notional = CsvLoaderUtils.parseDouble(row, addField(leg, LEG_NOTIONAL));
    if (productType == CoreProductType.IRS
        || productType == CoreProductType.SWAPTION
        || productType == CoreProductType.FRA) {
      return validatePositiveValue(notional, addField(leg, LEG_NOTIONAL));
    }
    return notional;
  }

  private String paymentCompounding(
      CsvRow row, String leg, Frequency paymentFreq, Frequency accrualFreq) {
    var fieldName = addField(leg, PAYMENT_SCHEDULE_COMPOUNDING_METHOD);
    var compounding =
        row.findValue(fieldName).map(CompoundingMethod::of).orElse(CompoundingMethod.NONE);
    return validateCompounding(fieldName, compounding, paymentFreq, accrualFreq).getName();
  }

  private CompoundingMethod validateCompounding(
      String fieldName, CompoundingMethod method, Frequency paymentFreq, Frequency accrualFreq) {
    return validateValue(
        method,
        fieldName,
        INVALID_COMPOUNDING,
        validateCompoundingMethod(accrualFreq, paymentFreq, method));
  }

  private Frequency accrualFreq(CsvRow row, String leg) {
    return parseFrequency(row, addField(leg, ACRUAL_SCHEDULE_FREQUENCY));
  }

  private Frequency paymentFreq(CsvRow row, String leg) {
    return parseFrequency(row, addField(leg, PAYMENT_SCHEDULE_FREQUENCY));
  }

  private Frequency parseFrequency(CsvRow row, String fieldName) {
    return CsvLoaderUtils.parseFrequency(
        row.getValue(fieldName), fieldName, Constants.SWAP_LEG_FREQUENCIES);
  }

  private PayReceive payReceive(CsvRow row, String leg) {
    return parsePayReceive(row.getValue(addField(leg, PAY_RECEIVE)));
  }

  private Integer paymentOffset(CsvRow row, String leg) {
    return row.findValue(addField(leg, PAYMENT_SCHEDULE_DATE_OFFSET))
        .map(Integer::parseInt)
        .orElse(0);
  }

  private Frequency parseIborFrequency(
      CsvRow row,
      String frequency,
      String fieldName,
      IborIndex iborIndex,
      Set<Frequency> permissibleFrequencies) {
    if (frequency.equals(ACRUAL_SCHEDULE_FREQUENCY)) {
      return row.findValue(fieldName)
          .map(freq -> CsvLoaderUtils.parseFrequency(freq, fieldName, permissibleFrequencies))
          .orElseGet(() -> indexAccrualFrequency(iborIndex));
    } else {
      var frequencyStr = row.getValue(fieldName);
      return CsvLoaderUtils.parseFrequency(frequencyStr, fieldName, permissibleFrequencies);
    }
  }

  private static boolean offshoreLeg(
      CsvRow row, String leg, Supplier<Optional<ErrorItem>> validator) {
    return row.findValue(addField(leg, OFFSHORE))
        .map(CsvLoaderUtils::parseBoolean)
        .filter(BooleanUtils::isTrue)
        .map(c -> validateValue(c, addField(leg, OFFSHORE), validator))
        .orElse(false);
  }

  IborIndex iborIndex(CsvRow row, String leg, CoreProductType productType) {
    var indexStr = row.getValue(addField(leg, CALCULATION_IBOR_INDEX));
    var index = IborIndex.of(indexStr);
    if (productType.equals(CoreProductType.SWAPTION)) {
      return validateValue(index, SWAPTION_INDICES);
    } else {
      return validateValue(index, IBOR_INDICES);
    }
  }

  <T extends FloatingRateIndex> Currency indexCurrency(CsvRow row, String leg, T index) {
    var ccy = legCurrency(row, leg);
    return validateValue(
        ccy,
        addField(leg, LEG_CURRENCY),
        String.format(INVALID_CURRENCY, ccy, index),
        index.getCurrency().equals(ccy));
  }

  Currency legCurrency(CsvRow row, String leg) {
    return Currency.of(row.getValue(addField(leg, LEG_CURRENCY)));
  }

  private PriceIndex priceIndex(CsvRow row, String leg) {
    var indexStr = row.getValue(addField(leg, CALCULATION_INFLATION_INDEX));
    var index = PriceIndex.of(indexStr);
    return validateValue(index, INFLATION_INDICES);
  }

  private Period parseInflationLag(String value, String fieldName) {
    var parsedInteger = Checked.now(() -> Integer.parseInt(value)).toOptional();
    if (parsedInteger.isPresent()) {
      int lag = parsedInteger.get();
      validateValue(lag, fieldName, INVALID_LAG_INTEGER, lag > 0);
      return Tenor.ofMonths(lag).getPeriod();
    }

    var validPeriod = ValidPeriodValidator.isValid(value, PeriodStyle.YM);
    var validated = validateValue(value, fieldName, INVALID_LAG_INTEGER, validPeriod);

    return Tenor.parse(validated).getPeriod();
  }

  private PriceIndexCalculationMethod parseCalculationMethod(
      CsvRow row, String methodStr, PriceIndexCalculationMethod defaultMethod) {
    return row.findValue(methodStr)
        .map(v -> validateValue(v, INDEX_CALC_METHOD_SUPPLIER))
        .map(PriceIndexCalculationMethod::of)
        .orElse(defaultMethod);
  }

  private SwapConvention parseSwapConvention(CsvRow row, CoreProductType productType) {
    var legs = List.of(parseLegKey(LEG_1, row, productType), parseLegKey(LEG_2, row, productType));

    return SwapTradeConventions.resolveStrataConvention(legs).orElse(null);
  }

  private SwapLegKey parseLegKey(String leg, CsvRow row, CoreProductType productType) {
    return switch (parseRateCalculationType(leg, row)) {
      case FIXED -> SwapLegKey.fixedLeg();
      case IBOR -> SwapLegKey.ibor(iborIndex(row, leg, productType));
      case OVERNIGHT -> SwapLegKey.overnight(overnightIndex(row, leg));
      case INFLATION -> SwapLegKey.inflation(priceIndex(row, leg));
      case TERM_OVERNIGHT -> SwapLegKey.termOvernight(termOvernightIndex(row, leg));
    };
  }
}
