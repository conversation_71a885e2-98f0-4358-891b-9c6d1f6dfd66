package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.portfolio.CoreProductType.FRA;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_FIXED_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_FIXED_RATE_VALUE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_IBOR_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_IDENTIFIER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.csv.SwapLegsValidator.validateLegs;
import static io.atlassian.fugue.Either.left;
import static java.lang.String.format;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.collect.result.ParseFailureException;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableFraDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvLoader;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class FraTradeCsvLoader implements ProductCsvLoader {
  private final FullSwapTradeCsvLoader commonLoader;

  @Override
  public List<ProductType> productTypes() {
    return List.of(FRA);
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    try {
      var accrualDates = TradeCsvLoaderUtils.parseAccrualDates(row);
      Optional<PositionType> optionalPositionType =
          row.findValue(TRADE_POSITION).map(PositionType::valueOf);

      var leg1 = parseLeg(row, LEG_1, refSecTrade, optionalPositionType);
      var leg2 = parseLeg(row, LEG_2, refSecTrade, optionalPositionType);

      // Derive positionType from legs for FRA
      // BUY = PAY FIXED, RECEIVE [BUY.toPayReceive()] IBOR;
      // SELL = PAY [SELL.toPayReceive()] IBOR, RECEIVE FIXED
      PositionType positionType =
          optionalPositionType.orElseGet(
              () ->
                  (leg1.getPayReceive() == PayReceive.PAY)
                          != (leg1.getCalculationType() == CalculationType.IBOR)
                      ? PositionType.BUY
                      : PositionType.SELL);

      var iborLeg = leg1.getCalculationType() == CalculationType.IBOR ? leg1 : leg2;
      var initialValue =
          CsvLoaderUtils.parseDouble(
              row, addField(iborLeg == leg1 ? LEG_2 : LEG_1, CALCULATION_FIXED_RATE_VALUE));
      var index = commonLoader.iborIndex(row, iborLeg == leg1 ? LEG_1 : LEG_2, FRA);

      var builder =
          ResolvableFraDetails.builder()
              .startDate(accrualDates.getStartDate())
              .endDate(accrualDates.getEndDate())
              .currency(parseTradeCcy(row))
              .notional(leg1.getNotional())
              .businessDayConvention(commonLoader.businessDayConvention(row))
              .index(index.getName())
              .positionType(positionType)
              .dayCount(index.getDayCount())
              .initialValue(initialValue);

      return validateLegs(FRA, positionType, List.of(leg1, leg2), row)
          .map(validateLegs -> builder.build());

    } catch (RuntimeException ex) {
      return left(
          PARSING_ERROR.entity(
              format("Error at line number %s. Error: %s", row.lineNumber(), ex.getMessage())));
    }
  }

  private ResolvableTradeLegDetails parseLeg(
      CsvRow row, String leg, boolean refSecTrade, Optional<PositionType> positionType) {
    var calculationType = commonLoader.parseRateCalculationType(leg, row);
    return switch (calculationType) {
      case FIXED -> parseFixedLeg(row, leg, CoreProductType.FRA, refSecTrade, positionType);
      case IBOR -> parseIborLeg(row, leg, CoreProductType.FRA, refSecTrade, positionType);
      // FRA does not have Overnight, Inflation, Term Overnight legs, but we call the commonLoader
      // methods here
      // to ensure proper validation and return meaningful error messages if such legs are
      // encountered.
      case OVERNIGHT -> commonLoader.parseOvernightLeg(row, leg, CoreProductType.FRA);
      case INFLATION -> commonLoader.parseInflationLeg(row, leg, CoreProductType.FRA);
      case TERM_OVERNIGHT -> commonLoader.parseTermOvernightLeg(row, leg, CoreProductType.FRA);
    };
  }

  private ResolvableTradeLegDetails parseFixedLeg(
      CsvRow row,
      String leg,
      CoreProductType productType,
      boolean refSecTrade,
      Optional<PositionType> positionType) {
    var identifier = commonLoader.legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var dayCount = row.getValue(addField(leg, CALCULATION_FIXED_DAY_COUNT));
    var initialValue = CsvLoaderUtils.parseDouble(row, addField(leg, CALCULATION_FIXED_RATE_VALUE));
    var payReceive =
        payReceiveWithDefault(
            row, leg, () -> positionType.map(PositionType::toPayReceive).map(PayReceive::opposite));

    return ResolvableFixedLeg.builder()
        .extLegIdentifier(identifier)
        .dayCount(
            CsvLoaderUtils.parseDefaultDayCount(
                dayCount, addField(leg, CALCULATION_FIXED_DAY_COUNT)))
        .initialValue(initialValue)
        .notional(notionalForFra(row, leg, productType, refSecTrade))
        .currency(commonLoader.legCurrency(row, leg))
        .payReceive(payReceive)
        .build();
  }

  private ResolvableTradeLegDetails parseIborLeg(
      CsvRow row,
      String leg,
      CoreProductType productType,
      boolean refSecTrade,
      Optional<PositionType> positionType) {
    var identifier = commonLoader.legIdentifier(row, addField(leg, LEG_IDENTIFIER));
    var index = commonLoader.iborIndex(row, leg, productType);
    var dayCount =
        row.findValue(addField(leg, CALCULATION_IBOR_DAY_COUNT))
            .map(
                v ->
                    CsvLoaderUtils.parseDefaultDayCount(
                        v, addField(leg, CALCULATION_IBOR_DAY_COUNT)))
            .orElse(index.getDayCount());
    var payReceive =
        payReceiveWithDefault(row, leg, () -> positionType.map(PositionType::toPayReceive));

    return ResolvableIborLeg.builder()
        .extLegIdentifier(identifier)
        .index(index.getName())
        .dayCount(dayCount)
        .notional(notionalForFra(row, leg, productType, refSecTrade))
        .currency(commonLoader.indexCurrency(row, leg, index))
        .payReceive(payReceive)
        .build();
  }

  @Nullable
  private Double notionalForFra(
      CsvRow row, String leg, CoreProductType productType, boolean refSecTrade) {
    if (refSecTrade) {
      return null;
    }
    return commonLoader.notional(row, leg, productType);
  }

  /**
   * Fetch the pay/receive value from the row. If not found, use the position type to obtain a
   * default value, if possible. If the pay/receive is not declared explicitly and position type is
   * not set, throw a parsing exception.
   *
   * @param row the CSV row
   * @param leg the leg prefix
   * @param defaultValueSupplier supplier of optional default value to use if the pay/receive is not
   *     found
   * @return the non-null pay/receive value
   */
  private static PayReceive payReceiveWithDefault(
      CsvRow row, String leg, Supplier<Optional<? extends PayReceive>> defaultValueSupplier) {
    return row.findValue(addField(leg, PAY_RECEIVE))
        .map(CsvLoaderUtils::parsePayReceive)
        .or(defaultValueSupplier)
        .orElseThrow(
            () ->
                new ParseFailureException(
                    "No value was found for '{header}' and '{buySell}' was not set",
                    addField(leg, PAY_RECEIVE),
                    TRADE_POSITION));
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    return commonLoader.tradeCurrency(row);
  }
}
