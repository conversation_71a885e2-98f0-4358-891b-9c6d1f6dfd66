package com.solum.xplain.valuation.calculation.swap;

import static com.solum.xplain.valuation.calculation.breakeven.SwapParRateCalculationsFactory.parRateCalculation;
import static com.solum.xplain.valuation.metrics.CashFlowMetrics.ofPayments;
import static com.solum.xplain.valuation.metrics.CashFlowValues.payValues;
import static com.solum.xplain.valuation.metrics.CashFlowValues.receiveValues;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.pricer.swap.DiscountingSwapLegPricer;
import com.opengamma.strata.pricer.swap.DiscountingSwapTradePricer;
import com.opengamma.strata.product.swap.RatePaymentPeriod;
import com.opengamma.strata.product.swap.ResolvedSwapLeg;
import com.opengamma.strata.product.swap.ResolvedSwapTrade;
import com.opengamma.strata.product.swap.SwapLegType;
import com.opengamma.strata.product.swap.SwapTrade;
import com.solum.xplain.extensions.pricers.XplainSwapTradePricers;
import com.solum.xplain.extensions.pricers.EnhancedXplainSwapTradePricers;
import com.solum.xplain.valuation.calculation.TradeCalculationUtils;
import com.solum.xplain.valuation.calculation.TradeCalculator;
import com.solum.xplain.valuation.calculation.TradeUtils;
import com.solum.xplain.valuation.calculation.ValuationOptions;
import com.solum.xplain.valuation.calculation.fx.FxUnderlyingCalculationUtils;
import com.solum.xplain.valuation.calculation.sensitivity.Dv01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.Pv01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.Spot01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.Spot01CalculationResult;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve;
import com.solum.xplain.valuation.messages.calibration.rates.FxShiftedValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import com.solum.xplain.valuation.metrics.CashFlowMetrics;
import com.solum.xplain.valuation.metrics.CashFlowValue;
import com.solum.xplain.valuation.metrics.CashFlowValues;
import com.solum.xplain.valuation.metrics.InflationMetrics;
import com.solum.xplain.valuation.metrics.Metrics;
import io.atlassian.fugue.extensions.step.Steps;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@ToString
@EqualsAndHashCode
@Slf4j
public class SwapTradeCalculator implements TradeCalculator {

  private static final LegAccrualDaysCalculator ACCRUAL_DAYS_CALCULATOR =
      LegAccrualDaysCalculator.DEFAULT;
  private static final InflationIndexValuesCalculator INFLATION_INDEX_VALUES_CALCULATOR =
      InflationIndexValuesCalculator.DEFAULT;

  private final SwapTrade trade;
  private final ValuationOptions options;
  private final Supplier<Map<String, CalibrationCurve>> curvesSupplier;
  private final RatesProvider ratesProvider;
  private final List<FxShiftedValuationCurveRates> fxShiftedRates;

  public SwapTradeCalculator(
      SwapTrade trade,
      ValuationOptions options,
      Supplier<Map<String, CalibrationCurve>> curvesSupplier,
      ValuationCurveRates rates) {
    this.trade = trade;
    this.options = options;
    this.curvesSupplier = curvesSupplier;
    this.ratesProvider = rates.getRatesProvider();
    this.fxShiftedRates = rates.getFxShiftedRates();
  }

  @Override
  public Metrics calculate(ReferenceData refData) {
    XplainSwapTradePricers swapTradePricers = new XplainSwapTradePricers(refData);
    EnhancedXplainSwapTradePricers enhancedPricers = new EnhancedXplainSwapTradePricers(refData);
    log.info("Using swap trade pricers: {}", swapTradePricers);
    return new SwapTradeCalculatorDelegate(
            trade,
            options,
            curvesSupplier,
            ratesProvider,
            fxShiftedRates,
            enhancedPricers.getLegPricer(),
            enhancedPricers.getTradePricer())
        .calculate(refData);
  }

  @RequiredArgsConstructor
  private static class SwapTradeCalculatorDelegate {
    private final SwapTrade trade;
    private final ValuationOptions options;
    private final Supplier<Map<String, CalibrationCurve>> curvesSupplier;
    private final RatesProvider ratesProvider;
    private final List<FxShiftedValuationCurveRates> shiftedRates;
    private final DiscountingSwapLegPricer legPricer;
    private final DiscountingSwapTradePricer swapTradePricer;

    private Metrics calculate(ReferenceData refData) {

      var reportingCcy = options.getReportingCcy();
      var tradeCcy = options.getTradeCcy();
      var resolvedTrade = trade.resolve(refData);

      var dirtyPVRepCcy = swapTradePricer.presentValue(resolvedTrade, reportingCcy, ratesProvider);
      var dirtyPVTradeCcy = dirtyPVRepCcy.convertedTo(tradeCcy, ratesProvider);

      var payCashFlows = payCashFlows(resolvedTrade);
      var receiveCashFlows = receiveCashFlows(resolvedTrade);
      var cashFlows = payCashFlows.combinedWith(receiveCashFlows).compact().getValues();

      var cfMetrics = cashFlowMetrics(resolvedTrade);
      var pvRepCcy = dirtyPVRepCcy.getAmount() - cfMetrics.tZeroNetReportingCcy();
      var pvPayLegCcy = dirtyPVTradeCcy.getAmount() - cfMetrics.tZeroNet();
      var metricsBuilder = Metrics.builder(TradeUtils.getClientPv(trade));

      var payLeg = resolvedTrade.getProduct().getPayLeg().orElseThrow(() -> legMissing("Pay"));
      var paylegMetrics = legMetrics(payLeg, ratesProvider, payCashFlows, cfMetrics.tZeroPay());

      var recLeg = resolvedTrade.getProduct().getReceiveLeg().orElseThrow(() -> legMissing("Rec"));
      var recLegMetrics = legMetrics(recLeg, ratesProvider, receiveCashFlows, cfMetrics.tZeroRec());

      var accruedInterest =
          recLegMetrics
              .accruedWithUnpaidCoupon()
              .convertedTo(reportingCcy, ratesProvider)
              .plus(
                  paylegMetrics.accruedWithUnpaidCoupon().convertedTo(reportingCcy, ratesProvider));

      var cleanPv = dirtyPVRepCcy.minus(accruedInterest.plus(cfMetrics.tZeroNetReportingCcy()));
      var breakevenMetrics =
          TradeCalculationUtils.eitherBreakevenMetrics(
              resolvedTrade, () -> breakevenMetrics(refData));
      var inflationMetrics = inflationMetrics(resolvedTrade);

      metricsBuilder
          .localCcy(tradeCcy.getCode())
          .presentValue(pvRepCcy)
          .presentValuePayLegCurrency(pvPayLegCcy)
          .accrued(accruedInterest.getAmount())
          .accruedLocalCcy(accruedInterest.convertedTo(tradeCcy, ratesProvider).getAmount())
          .payLegPV(paylegMetrics.pv.getAmount())
          .payLegAccrued(paylegMetrics.accrued.getAmount())
          .payLegAccruedDays(paylegMetrics.accruedDays)
          .receiveLegPV(recLegMetrics.pv.getAmount())
          .receiveLegAccrued(recLegMetrics.accrued.getAmount())
          .receiveLegAccruedDays(recLegMetrics.accruedDays)
          .cashFlows(cashFlows)
          .cashFlowMetrics(cfMetrics)
          .cleanPresentValue(cleanPv.getAmount())
          .cleanPresentValueLocalCcy(cleanPv.convertedTo(tradeCcy, ratesProvider).getAmount())
          .breakevenMetrics(breakevenMetrics)
          .inflationMetrics(inflationMetrics);

      Pv01Calculation.DEFAULT
          .calculatePv01(
              trade,
              dirtyPVRepCcy,
              cfMetrics.getTZeroNet(),
              ratesProvider,
              refData,
              swapTradePricer,
              t -> cashFlowMetrics(t).getTZeroNet())
          .ifPresent(
              pv01 -> {
                metricsBuilder.pv01(pv01.getAmount());
                metricsBuilder.pv01LocalCcy(pv01.convertedTo(tradeCcy, ratesProvider).getAmount());
              });

      if (resolvedTrade.getProduct().isCrossCurrency()) {
        addXccyFxSpotRate(metricsBuilder, payLeg.getCurrency(), recLeg.getCurrency());
      }

      if (options.isCalculateSensitivities()) {
        var curves = curvesSupplier.get();
        var dv01Calculation = dv01Calculation(resolvedTrade, curves);
        metricsBuilder.with(dv01Calculation.calculate(ratesProvider));
        var spot01 = calculateSpot01(dirtyPVTradeCcy, resolvedTrade);
        metricsBuilder.with(spot01);
      }
      return metricsBuilder.build();
    }

    /**
     * Adds the FX spot rate to the metrics builder for XCCY assuming the Xplain underlying is in
     * the form "USD vs EUR". This is format used by {@code
     * com.solum.xplain.core.portfolio.trade.details.XccyDetailsResolver.resolveUnderlying}.
     *
     * <p>If no valid currency pair can be created from the xplainResolvedTradeUnderlying, a warning
     * is logged and the FX spot rate is not added to the metrics builder.
     *
     * @param metricsBuilder builder to add the FX spot rate to
     * @param payCcy pay leg currency
     * @param recCcy receive leg currency
     * @return the updated metrics builder
     * @see ValuationOptions#getXplainResolvedTradeUnderlying()
     */
    private Metrics.MetricsBuilder addXccyFxSpotRate(
        Metrics.MetricsBuilder metricsBuilder, Currency payCcy, Currency recCcy) {
      var currencyPair = CurrencyPair.of(payCcy, recCcy).toConventional();
      var xplainResolvedTradeUnderlying = options.getXplainResolvedTradeUnderlying();
      try {
        var fxUnderlying = xplainResolvedTradeUnderlying.replace(" vs ", "/");
        var xplainFxUnderlying = CurrencyPair.parse(fxUnderlying);
        var fxSpotRate =
            FxUnderlyingCalculationUtils.fxSpot(
                currencyPair, xplainFxUnderlying.toString(), ratesProvider);
        metricsBuilder.fxSpot(fxSpotRate);
      } catch (IllegalArgumentException e) {
        log.warn(
            "xplainResolvedTradeUnderlying {} could not be converted to currency pair due to: {}",
            xplainResolvedTradeUnderlying,
            e.getMessage());
      }
      return metricsBuilder;
    }

    private Optional<RatePaymentPeriod> paymentPeriod(
        ResolvedSwapLeg leg, LocalDate valuationDate) {
      return leg.getPaymentPeriods().stream()
          .filter(period -> !period.getPaymentDate().isBefore(valuationDate))
          .filter(RatePaymentPeriod.class::isInstance)
          .map(RatePaymentPeriod.class::cast)
          .findFirst();
    }

    private CashFlowValues payCashFlows(ResolvedSwapTrade trade) {
      return trade
          .getProduct()
          .getPayLeg()
          .map(leg -> legPricer.cashFlows(leg, ratesProvider))
          .map(c -> payValues(c, options.getReportingCcy(), ratesProvider))
          .orElse(CashFlowValues.empty());
    }

    private CashFlowValues receiveCashFlows(ResolvedSwapTrade trade) {
      return trade
          .getProduct()
          .getReceiveLeg()
          .map(leg -> legPricer.cashFlows(leg, ratesProvider))
          .map(c -> receiveValues(c, options.getReportingCcy(), ratesProvider))
          .orElse(CashFlowValues.empty());
    }

    private CashFlowMetrics cashFlowMetrics(ResolvedSwapTrade resolvedSwapTrade) {
      var product = resolvedSwapTrade.getProduct();
      return Steps.begin(product.getPayLeg())
          .then(product::getReceiveLeg)
          .yield(
              (pay, receive) -> {
                var ccPay = legPricer.currentCash(pay, ratesProvider);
                var ccRec = legPricer.currentCash(receive, ratesProvider);
                return ofPayments(
                    ccPay,
                    ccRec,
                    null,
                    options.getReportingCcy(),
                    options.getTradeCcy(),
                    ratesProvider);
              })
          .orElse(CashFlowMetrics.builder().build());
    }

    private LegMetrics legMetrics(
        ResolvedSwapLeg leg,
        RatesProvider ratesProvider,
        CashFlowValues cashFlowValues,
        Double tZeroCashflow) {
      var valuationDate = ratesProvider.getValuationDate();
      var unpaidCoupon =
          Steps.begin(paymentPeriod(leg, ratesProvider.getValuationDate()))
              .then(cashFlowValues::firstCashFlow)
              .yield(
                  (paymentPeriod, cashFlow) ->
                      legUnpaidCoupon(leg, cashFlow, valuationDate, paymentPeriod))
              .orElse(CurrencyAmount.zero(leg.getCurrency()));
      // accrued interest = accrued - today's cashflow
      var accrued =
          legPricer.accruedInterest(leg, ratesProvider).plus(unpaidCoupon).minus(tZeroCashflow);
      var accruedDays = ACCRUAL_DAYS_CALCULATOR.accruedDays(leg, ratesProvider.getValuationDate());
      var pv = legPricer.presentValue(leg, ratesProvider).minus(tZeroCashflow);

      return LegMetrics.newOf(pv, accrued, accruedDays);
    }

    // returns unpaid coupon if valuation date > coupon date && valuation date <= payment date
    private CurrencyAmount legUnpaidCoupon(
        ResolvedSwapLeg leg,
        CashFlowValue firstCashFlow,
        LocalDate valuationDate,
        RatePaymentPeriod paymentPeriod) {

      var paymentDate = paymentPeriod.getPaymentDate();
      var couponDate = paymentPeriod.getEndDate();

      if (valuationDate.isAfter(couponDate) && !paymentDate.isBefore(valuationDate)) {
        var forecastValue =
            leg.getPayReceive().isPay()
                ? firstCashFlow.getForecastPay()
                : firstCashFlow.getForecastReceive();
        return CurrencyAmount.of(firstCashFlow.getCurrency(), forecastValue);
      }
      return CurrencyAmount.zero(Currency.parse(firstCashFlow.getCurrency()));
    }

    private BreakevenMetrics breakevenMetrics(ReferenceData refData) {
      var parRate = parRateCalculation(trade).calculate(refData, ratesProvider);
      return BreakevenMetrics.parRate(parRate);
    }

    private Dv01Calculation dv01Calculation(
        ResolvedSwapTrade trade, Map<String, CalibrationCurve> curves) {
      var sensitivities = swapTradePricer.presentValueSensitivity(trade, ratesProvider);
      return new Dv01Calculation(
          options.getReportingCcy(), options.getTradeCcy(), sensitivities, curves);
    }

    private Spot01CalculationResult calculateSpot01(
        CurrencyAmount dirtyPv, ResolvedSwapTrade resolvedTrade) {
      var spot01Calculation = Spot01Calculation.fromRatesProvider(ratesProvider, shiftedRates);
      return spot01Calculation.calculateSpot01(
          dirtyPv,
          (ccy, provider) ->
              swapTradePricer.presentValue(resolvedTrade, dirtyPv.getCurrency(), provider));
    }

    private IllegalArgumentException legMissing(String legName) {
      return new IllegalArgumentException(String.format("%s leg is required", legName));
    }

    private InflationMetrics inflationMetrics(ResolvedSwapTrade resolvedSwapTrade) {
      var inflationLegPaymentPeriods =
          resolvedSwapTrade.getProduct().getLegs().stream()
              .filter(leg -> leg.getType() == SwapLegType.INFLATION)
              .flatMap(leg -> leg.getPaymentPeriods().stream())
              .filter(RatePaymentPeriod.class::isInstance)
              .map(RatePaymentPeriod.class::cast)
              .toList();

      return INFLATION_INDEX_VALUES_CALCULATOR.calculateInflationMetrics(
          inflationLegPaymentPeriods, ratesProvider);
    }

    @AllArgsConstructor(staticName = "newOf")
    private static class LegMetrics {

      private final CurrencyAmount pv;
      private final CurrencyAmount accrued;
      private final Integer accruedDays;

      public CurrencyAmount accruedWithUnpaidCoupon() {
        return accrued;
      }
    }
  }
}
